// Unidirectional Data Flow Implementation
document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const requestsListEl = document.getElementById('requests-list');
    const addNewRequestBtn = document.getElementById('add-new-request-btn');
    const sortBtn = document.getElementById('sort-btn');
    const sortMenuEl = document.getElementById('sort-menu');
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const searchInputEl = document.getElementById('search-input');
    const requestNameInput = document.getElementById('request-name');
    const saveNameBtn = document.getElementById('save-name-btn');
    const bodyTypeSelect = document.getElementById('body-type-select');
    const advancedOptionsEl = document.getElementById('advanced-options');
    const httpMethodSelect = document.getElementById('http-method');
    const urlInput = document.getElementById('url');
    const headersContainerEl = document.getElementById('headers-container');
    const addHeaderBtn = document.getElementById('add-header-btn');
    const bodyInput = document.getElementById('body-input'); // Raw body
    const simpleBodyEditorEl = document.getElementById('simple-body-editor');
    const advancedBodyEditorEl = document.getElementById('advanced-body-editor');
    const simpleBodyContainerEl = document.getElementById('simple-body-container');
    const formDataEditorEl = document.getElementById('form-data-editor');
    const formDataContainerEl = document.getElementById('form-data-container');
    const addFormDataRowBtn = document.getElementById('add-form-data-row-btn');
    const valueCharWordCountEl = document.getElementById('value-char-word-count');
    const charCountEl = document.getElementById('char-count');
    const wordCountEl = document.getElementById('word-count');
    const addSimpleRowBtn = document.getElementById('add-simple-row-btn');
    const testUrlBtn = document.getElementById('test-url-btn');
    const sendBtn = document.getElementById('send-btn');
    const deleteRequestBtn = document.getElementById('delete-request-btn');
    const exportBtn = document.getElementById('export-btn');
    const importFileEl = document.getElementById('import-file');
    const clearRawBodyBtn = document.getElementById('clear-raw-body-btn');
    const notificationBarEl = document.getElementById('notification-bar');
    const responseViewerEl = document.getElementById('response-viewer');
    const responseStatusEl = document.getElementById('response-status');
    const responseBodyEl = document.getElementById('response-body');
    const extremeCleanCheckbox = document.getElementById('extreme-clean-checkbox');

    // --- State Management ---
    let state = {
        requests: [],
        selectedRequestId: null,
        sortBy: 'useCount', // Default sort
        searchQuery: '',
        settings: {
            extremeClean: false,
            keepData: false, // Add keepData to the default state
        },
        // Upload status tracking
        activeUpload: {
            requestId: null,
            isUploading: false,
            startTime: null,
            hasLargeFiles: false
        }
    };
    let deleteFieldConfirmState = { type: null, index: null };
    let deleteFieldTimeout = null;
    let originalRequestName = '';

    // --- Core Functions ---

    /**
     * Initialize notification bar with proper hidden state
     */
    function initializeNotificationBar() {
        notificationBarEl.style.opacity = '0';
        notificationBarEl.style.visibility = 'hidden';
        notificationBarEl.style.transform = 'translateX(-50%) translateY(-20px)';
    }

    /**
     * Gets an appropriate icon for the file type
     * @function getFileTypeIcon
     * @param {string} extension - File extension including the dot
     * @returns {string} HTML for the file type icon
     */
    function getFileTypeIcon(extension) {
        const ext = extension.toLowerCase();

        // Video files
        if (['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'].includes(ext)) {
            return '📹'; // Video camera emoji
        }

        // Image files
        if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico', '.tiff'].includes(ext)) {
            return '🖼️'; // Picture frame emoji
        }

        // Audio files
        if (['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'].includes(ext)) {
            return '🎧'; // Headphones emoji
        }

        // Document files
        if (['.pdf', '.doc', '.docx', '.txt', '.rtf'].includes(ext)) {
            return '📄'; // Document emoji
        }

        // Spreadsheet files
        if (['.xls', '.xlsx', '.csv'].includes(ext)) {
            return '📈'; // Chart emoji
        }

        // Archive files
        if (['.zip', '.rar', '.7z', '.tar', '.gz'].includes(ext)) {
            return '🗇'; // Archive emoji
        }

        // Code files
        if (['.js', '.html', '.css', '.json', '.xml', '.py', '.java', '.cpp', '.c'].includes(ext)) {
            return '💻'; // Computer emoji
        }

        // Default file icon
        return '📁'; // File folder emoji
    }



    /**
     * Checks if the file is a video file
     * @function isVideoFile
     * @param {string} extension - File extension including the dot
     * @returns {boolean} True if video file
     */
    function isVideoFile(extension) {
        const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'];
        return videoExtensions.includes(extension.toLowerCase());
    }

    /**
     * Checks if the file is an image file
     * @function isImageFile
     * @param {string} extension - File extension including the dot
     * @returns {boolean} True if image file
     */
    function isImageFile(extension) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico', '.tiff'];
        return imageExtensions.includes(extension.toLowerCase());
    }

    /**
     * Formats file size in human-readable format
     * @function formatFileSize
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Generates a thumbnail for an image file
     * @function generateThumbnail
     * @param {string} dataUrl - The data URL of the image
     * @param {string} mimeType - The MIME type of the image
     * @returns {Promise<string>} Promise that resolves to thumbnail data URL
     */
    async function generateThumbnail(dataUrl, mimeType) {
        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                try {
                    // Create canvas for thumbnail
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // Set thumbnail dimensions (max 100x100, maintain aspect ratio)
                    const maxSize = 100;
                    let { width, height } = img;

                    if (width > height) {
                        if (width > maxSize) {
                            height = (height * maxSize) / width;
                            width = maxSize;
                        }
                    } else {
                        if (height > maxSize) {
                            width = (width * maxSize) / height;
                            height = maxSize;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // Draw image on canvas
                    ctx.drawImage(img, 0, 0, width, height);

                    // Convert to data URL (use JPEG for smaller size, unless it's PNG/GIF)
                    const outputFormat = mimeType === 'image/png' || mimeType === 'image/gif' ? mimeType : 'image/jpeg';
                    const thumbnailDataUrl = canvas.toDataURL(outputFormat, 0.8);

                    resolve(thumbnailDataUrl);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => {
                reject(new Error('Failed to load image for thumbnail generation'));
            };

            img.src = dataUrl;
        });
    }

    /**
     * Initializes the theme based on stored preferences
     * @async
     * @function initializeTheme
     * @returns {Promise<void>}
     */
    async function initializeTheme() {
        try {
            const { theme } = await chrome.storage.local.get({ theme: 'light' });
            document.documentElement.setAttribute('data-theme', theme);
            updateThemeIcon(theme);
        } catch (error) {
            // Fallback to light theme
            document.documentElement.setAttribute('data-theme', 'light');
            updateThemeIcon('light');
        }
    }

    /**
     * Updates the theme toggle button icon based on current theme
     * @function updateThemeIcon
     * @param {string} theme - Current theme ('light' or 'dark')
     */
    function updateThemeIcon(theme) {
        const themeIcon = themeToggleBtn.querySelector('svg path');
        if (theme === 'dark') {
            // Sun icon for switching to light mode
            themeIcon.setAttribute('d', 'M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z');
            themeToggleBtn.title = 'Switch to Light Mode';
        } else {
            // Moon icon for switching to dark mode
            themeIcon.setAttribute('d', 'M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z');
            themeToggleBtn.title = 'Switch to Dark Mode';
        }
    }

    /**
     * Toggles between light and dark theme
     * @async
     * @function toggleTheme
     * @returns {Promise<void>}
     */
    async function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        updateThemeIcon(newTheme);

        await chrome.storage.local.set({ theme: newTheme });
        // Removed unwanted theme switch notification - user preference
    }

    /**
     * Gets the last selected request ID from Chrome session storage
     * @async
     * @function getLastSelectedId
     * @returns {Promise<string|null>} Last selected request ID or null
     */
    async function getLastSelectedId() {
        try {
            const sessionData = await chrome.storage.session.get({ lastSelectedId: null });
            return sessionData.lastSelectedId;
        } catch (error) {
            return null;
        }
    }

    /**
     * Loads initial state from Chrome storage
     * @async
     * @function loadInitialState
     * @returns {Promise<void>}
     */
    async function loadInitialState() {
        try {

            const { requests, settings, sortBy, uploadState } = await chrome.storage.local.get({
                requests: [],
                settings: { extremeClean: false, keepData: false },
                sortBy: 'useCount',
                uploadState: null
            });

            state.requests = requests || [];
            state.settings = { ...state.settings, ...settings };
            state.sortBy = sortBy || 'useCount';



            // Restore upload state if exists
            if (uploadState && uploadState.isUploading) {
                state.activeUpload = uploadState;

                // Check if upload is still active and resume progress tracking
                await resumeProgressTracking();
            }

            // Update UI
            extremeCleanCheckbox.checked = state.settings.extremeClean;

            // Select the last selected request or the first one
            const lastSelectedId = await getLastSelectedId();
            if (lastSelectedId && state.requests.find(r => r.id === lastSelectedId)) {
                selectRequest(lastSelectedId);
            } else if (state.requests.length > 0) {
                selectRequest(state.requests[0].id);
            }

            render();
        } catch (error) {
            render();
        }
    }

    /**
     * Saves upload state to Chrome storage for persistence
     * @async
     * @function saveUploadState
     * @returns {Promise<void>}
     */
    async function saveUploadState() {
        try {
            await chrome.storage.local.set({
                uploadState: state.activeUpload
            });
        } catch (error) {
            // Failed to save upload state
        }
    }

    /**
     * Clears upload state from Chrome storage
     * @async
     * @function clearUploadState
     * @returns {Promise<void>}
     */
    async function clearUploadState() {
        try {
            await chrome.storage.local.remove('uploadState');
            state.activeUpload = {
                requestId: null,
                isUploading: false,
                startTime: null,
                hasLargeFiles: false
            };
        } catch (error) {
            // Failed to clear upload state
        }
    }

    /**
     * Resets the send button to its normal state
     * @function resetSendButton
     */
    function resetSendButton() {
        if (sendBtn) {
            sendBtn.classList.remove('loading', 'sending', 'error');
            sendBtn.disabled = false;
            sendBtn.style.backgroundColor = '';

            const btnText = sendBtn.querySelector('.btn-text');
            if (btnText) {
                // Check if there are files staged - if so, show "Ready to send..."
                // otherwise show "Send"



                if (state.activeUpload && state.activeUpload.hasLargeFiles) {
                    // For large files, show ready state with file info
                    const fileSizeMB = ((state.activeUpload.totalSize || 0) / (1024 * 1024)).toFixed(1);

                    btnText.innerHTML = `Ready to send... <div class="upload-info"><small>✅ ${fileSizeMB}MB staged</small></div>`;
                } else {
                    // Check if current request has formDataBody with files
                    const currentRequest = state.requests.find(r => r.id === state.selectedRequestId);


                    if (currentRequest && currentRequest.formDataBody) {
                        const hasFiles = currentRequest.formDataBody.some(item =>
                            item.type === 'file' && (item.value || item._isFileRef || item.fileName)
                        );



                        if (hasFiles) {
                            // Calculate total file size for display
                            let totalSize = 0;
                            currentRequest.formDataBody.forEach(item => {
                                if (item.type === 'file' && item.fileSize) {
                                    totalSize += item.fileSize;
                                }
                            });
                            const fileSizeMB = (totalSize / (1024 * 1024)).toFixed(1);

                            btnText.innerHTML = `Ready to send... <div class="upload-info"><small>✅ ${fileSizeMB}MB staged</small></div>`;
                        } else {

                            btnText.textContent = 'Send';
                            btnText.innerHTML = 'Send';
                        }
                    } else {

                        btnText.textContent = 'Send';
                        btnText.innerHTML = 'Send';
                    }
                }
            }
        }
    }

    /**
     * Handles upload completion and cleanup
     * @async
     * @function handleUploadComplete
     * @param {boolean} success - Whether upload was successful
     * @param {string} [message] - Optional completion message
     * @param {object} [responseData] - Optional response data from background script
     * @returns {Promise<void>}
     */
    async function handleUploadComplete(success, message, responseData = null) {


        // Clear any progress intervals
        if (state.activeUpload.progressInterval) {
            clearInterval(state.activeUpload.progressInterval);
            state.activeUpload.progressInterval = null;
        }

        // For successful uploads, show animated tick mark briefly
        if (success) {
            // Update button to show animated success state
            sendBtn.classList.remove('loading', 'sending');
            sendBtn.disabled = false;
            const btnText = sendBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.innerHTML = `<span class="animated-success">✓</span>`;
                sendBtn.style.backgroundColor = 'var(--success-color)';
            }

            // Reset after 2 seconds with animated tick mark
            setTimeout(() => {
                resetSendButton();
            }, 2000);
        } else {
            // For failed uploads, reset immediately
            resetSendButton();
        }

        // Clear upload state
        await clearUploadState();

        // Handle response display if we have response data
        if (responseData) {
            responseStatusEl.textContent = `Status: ${responseData.status}`;
            responseStatusEl.className = responseData.isSuccess ? 'status-success' : 'status-error';

            let responseText = responseData.body || responseData.error || 'No response body.';
            try {
                const jsonObj = JSON.parse(responseText);
                responseText = JSON.stringify(jsonObj, null, 2);
            } catch (e) {
                // Not JSON, just display as is
            }
            responseBodyEl.textContent = responseText;
            responseViewerEl.classList.remove('hidden');

            // Update request use count if successful
            if (success && responseData.isSuccess) {
                let currentRequest = state.requests.find(r => r.id === state.selectedRequestId);
                if (currentRequest) {
                    if (responseData.updatedRequest) {
                        const requestIndex = state.requests.findIndex(r => r.id === state.selectedRequestId);
                        if (requestIndex !== -1) {
                            state.requests[requestIndex] = responseData.updatedRequest;
                        }
                    } else {
                        currentRequest.useCount = (currentRequest.useCount || 0) + 1;
                        currentRequest.lastUsed = Date.now();
                    }
                    saveState();
                    render();
                }
            }
        }

        // Show notification
        if (success) {
            // Removed upload completion notification - UI already shows status
            // showNotification(message || '🚀 Upload completed successfully!', 'success');
        } else {
            showNotification(message || '❌ Upload failed', 'error');
        }
    }

    /**
     * Resumes progress tracking for ongoing upload
     * @async
     * @function resumeProgressTracking
     * @returns {Promise<void>}
     */
    async function resumeProgressTracking() {
        if (!state.activeUpload.isUploading || !state.activeUpload.requestId) {
            return;
        }



        // 🛡️ SAFETY CHECK: Don't resume if upload is too old (5 minutes)
        const uploadAge = Date.now() - (state.activeUpload.startTime || Date.now());
        const MAX_UPLOAD_AGE = 5 * 60 * 1000; // 5 minutes

        if (uploadAge > MAX_UPLOAD_AGE) {

            await clearUploadState();
            resetSendButton();
            return;
        }

        // Update UI to show uploading state
        sendBtn.classList.add('sending');
        sendBtn.disabled = true;
        const btnText = sendBtn.querySelector('.btn-text');
        btnText.innerHTML = 'Sending... <span class="progress-percentage">Checking...</span>';

        // 🛡️ SAFETY: Clear any existing intervals first
        if (state.activeUpload.progressInterval) {
            clearInterval(state.activeUpload.progressInterval);
        }

        // Start polling for progress
        const progressInterval = setInterval(() => {
            chrome.runtime.sendMessage({
                action: 'getProgress',
                requestId: state.activeUpload.requestId
            }, (progressResponse) => {
                // Check for chrome.runtime.lastError to handle message port issues
                if (chrome.runtime.lastError) {
                    // Only handle if it's not a common "port closed" error
                    if (!chrome.runtime.lastError.message.includes('port closed')) {
                        // Progress tracking error
                    }
                    clearInterval(progressInterval);
                    handleUploadComplete(true, 'Upload completed (progress tracking ended)');
                    return;
                }

                if (progressResponse && progressResponse.progress) {
                    const progress = progressResponse.progress;
                    const progressSpan = btnText.querySelector('.progress-percentage');

                    if (progressSpan && progress.progress !== undefined) {
                        progressSpan.textContent = `${progress.progress}%`;

                        // Update button style based on progress stage
                        if (progress.stage === 'complete') {
                            clearInterval(progressInterval);
                            progressSpan.textContent = '100%';
                            // Pass the final response data if available
                            handleUploadComplete(true, undefined, progress.finalResponse);
                        } else if (progress.stage === 'error') {
                            clearInterval(progressInterval);
                            progressSpan.textContent = 'Error';
                            handleUploadComplete(false, progress.error);
                        }
                    }
                } else {
                    // No progress found, upload might have completed while popup was closed

                    clearInterval(progressInterval);
                    handleUploadComplete(true, 'Upload may have completed while popup was closed');
                }
            });
        }, 500);

        // Store interval reference for cleanup
        state.activeUpload.progressInterval = progressInterval;
    }

    /**
     * Cancels an ongoing upload and cleans up resources
     * @async
     * @function cancelUpload
     * @returns {Promise<void>}
     */
    async function cancelUpload() {
        if (!state.activeUpload.isUploading) {
            return;
        }



        // Clear progress interval
        if (state.activeUpload.progressInterval) {
            clearInterval(state.activeUpload.progressInterval);
            state.activeUpload.progressInterval = null;
        }

        // Notify background script to cancel the request
        if (state.activeUpload.requestId) {
            try {
                chrome.runtime.sendMessage({
                    action: 'cancelUpload',
                    requestId: state.activeUpload.requestId
                });
            } catch (error) {
                // Failed to notify background script of cancellation
            }
        }

        // Reset UI
        resetSendButton();
        await clearUploadState();

        showNotification('🚫 Upload canceled by user', 'warning', 3000);
    }

    // Make cancelUpload globally available for onclick handler
    window.cancelUpload = cancelUpload;

    /**
     * Handles very large files by chunking them to avoid Chrome message size limits
     * @function handleVeryLargeFileUpload
     * @param {object} request - The request object
     * @param {string} requestId - Unique request ID
     * @param {Array} files - Array of file objects
     * @param {string} fileSizeMB - Total file size in MB
     */
    const handleVeryLargeFileUpload = async (request, requestId, files, fileSizeMB) => {
        try {

            // Maximum chunk size: 5MB (well under Chrome's 64MB message limit)
            const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB
            const chunks = [];
            let totalChunks = 0;

            // Process each file into chunks
            for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
                const file = files[fileIndex];
                const fileData = file.data; // Base64 data (corrected from file.value to file.data)
                const fileSize = file.size;

                // Validate file data exists and has length
                if (!fileData || typeof fileData !== 'string' || !fileData.length) {
                    throw new Error(`File data is missing or invalid for ${file.fileName || 'unknown file'} at index ${fileIndex}`);
                }

                // Calculate how many chunks needed for this file
                const numChunks = Math.ceil(fileData.length * 0.75 / CHUNK_SIZE); // 0.75 accounts for base64 overhead
                const chunkSizeInBase64 = Math.ceil(fileData.length / numChunks);

                for (let chunkIndex = 0; chunkIndex < numChunks; chunkIndex++) {
                    const start = chunkIndex * chunkSizeInBase64;
                    const end = Math.min(start + chunkSizeInBase64, fileData.length);
                    const chunkData = fileData.slice(start, end);

                    const chunkId = `chunk_${requestId}_${fileIndex}_${chunkIndex}`;
                    chunks.push({
                        id: chunkId,
                        fileIndex: fileIndex,
                        chunkIndex: chunkIndex,
                        totalChunks: numChunks,
                        data: chunkData,
                        fileName: file.fileName,
                        originalKey: file.key,
                        fileSize: fileSize
                    });
                    totalChunks++;
                }
            }



            // Store chunks in Chrome storage (in batches to avoid storage limits)
            const BATCH_SIZE = 10; // Store 10 chunks at a time
            let storedChunks = 0;

            for (let i = 0; i < chunks.length; i += BATCH_SIZE) {
                const batch = chunks.slice(i, i + BATCH_SIZE);
                const storageData = {};

                batch.forEach(chunk => {
                    storageData[chunk.id] = chunk;
                });

                await chrome.storage.local.set(storageData);
                storedChunks += batch.length;

                // Update progress
                const progress = Math.round((storedChunks / totalChunks) * 30); // 30% for chunking phase
                const btnText = sendBtn.querySelector('.btn-text');
                btnText.innerHTML = `Preparing... <div class="upload-info"><small>📦 Chunking ${fileSizeMB}MB (${storedChunks}/${totalChunks})</small><div class="progress-bar"><div class="progress-fill" style="width: ${progress}%"></div></div></div>`;

                // Small delay to prevent blocking UI
                await new Promise(resolve => setTimeout(resolve, 10));
            }



            // Create chunk metadata for the background script
            const chunkMetadata = {
                requestId: requestId,
                totalChunks: totalChunks,
                files: files.map((file, index) => ({
                    index: index,
                    fileName: file.fileName,
                    key: file.key,
                    size: file.size,
                    chunksCount: chunks.filter(c => c.fileIndex === index).length
                }))
            };

            // Create safe request without actual file data
            const safeRequest = {
                ...request,
                formDataBody: request.formDataBody ? request.formDataBody.map((item, index) => {
                    if (item.type === 'file' && item.value) {
                        return {
                            ...item,
                            value: null, // Remove file data
                            _isChunkedFile: true, // Mark as chunked file
                            _originalIndex: index
                        };
                    }
                    return item;
                }) : []
            };

            // Send chunked upload request to background
            chrome.runtime.sendMessage({
                action: 'executeChunkedRequest',
                requestData: safeRequest,
                requestId: requestId,
                chunkMetadata: chunkMetadata,
                _isVeryLargeFile: true
            }, (response) => {
                if (chrome.runtime.lastError) {
                    handleDirectUploadComplete(false, `Chunked upload failed: ${chrome.runtime.lastError.message}`, null, fileSizeMB);
                    return;
                }

                if (response && response.status === 'progress') {
                    // Handle progress tracking for chunked upload
                    handleDirectUploadResponse(response, requestId, fileSizeMB);
                } else {
                    handleDirectUploadComplete(response?.isSuccess || false, response?.error, response, fileSizeMB);
                }
            });

        } catch (error) {
            // Check for quota exceeded errors
            if (error.message && error.message.includes('quota exceeded')) {
                throw new Error('Storage quota exceeded. The file is too large for Chrome storage. Consider using the unlimitedStorage permission or reducing file size.');
            } else if (error.message && error.message.includes('QUOTA_BYTES')) {
                throw new Error('Chrome storage limit reached. Please reload the extension or try a smaller file.');
            }

            throw new Error(`Chunking failed: ${error.message}`);
        }
    };

    /**
     * Handles direct upload completion and cleanup for very large files
     * @function handleDirectUploadComplete
     * @param {boolean} success - Whether upload was successful
     * @param {string} [message] - Optional completion message
     * @param {object} [responseData] - Optional response data from background script
     * @param {string} fileSizeMB - File size in MB for display
     */
    async function handleDirectUploadComplete(success, message, responseData = null, fileSizeMB) {
        // Clean up UI
        sendBtn.classList.remove('loading', 'sending');
        sendBtn.disabled = false;
        const btnText = sendBtn.querySelector('.btn-text');

        if (success) {
            btnText.innerHTML = `<span class="animated-success">✓</span>`;
            sendBtn.style.backgroundColor = 'var(--success-color)';
            // Removed large file upload notification - UI already shows completion status
            // showNotification(`🚀 Large file upload completed successfully! (${fileSizeMB}MB)`, 'success', 4000);

            // Update request state if we have response data
            if (responseData && responseData.updatedRequest) {
                const currentRequest = state.requests.find(r => r.id === state.selectedRequestId);
                if (currentRequest) {
                    const requestIndex = state.requests.findIndex(r => r.id === state.selectedRequestId);
                    if (requestIndex !== -1) {
                        state.requests[requestIndex] = responseData.updatedRequest;

                        saveState();
                        render();
                    }
                }
            }
        } else {
            btnText.innerHTML = `<span style="color: var(--error-color)">✗ Failed</span> <div class="upload-info"><small>🚨 ${fileSizeMB}MB upload failed</small></div>`;
            sendBtn.style.backgroundColor = 'var(--error-color)';
            // Removed upload failure notification - UI already shows error status
            // showNotification(message || `Large file upload failed (${fileSizeMB}MB)`, 'error', 5000);
        }

        // Reset button after 2 seconds - consistent with other upload methods
        setTimeout(() => {
            resetSendButton();
        }, 2000);

        // Clear any upload state
        await clearUploadState();
    }

    /**
     * Handles response from direct upload method for very large files
     * @function handleDirectUploadResponse
     * @param {object} response - Response from background script
     * @param {string} requestId - Request ID
     * @param {string} fileSizeMB - File size in MB
     */
    function handleDirectUploadResponse(response, requestId, fileSizeMB) {
        if (chrome.runtime.lastError) {
            if (!chrome.runtime.lastError.message.includes('port closed')) {
                showNotification('Communication error during large file upload. Please try again.', 'error', 4000);
            }
            handleDirectUploadComplete(false, 'Communication error', null, fileSizeMB);
            return;
        }

        if (response) {
            // Handle immediate response for very large files
            responseStatusEl.textContent = `Status: ${response.status}`;
            responseStatusEl.className = response.isSuccess ? 'status-success' : 'status-error';

            let responseText = response.body || response.error || 'No response body.';
            try {
                const jsonObj = JSON.parse(responseText);
                responseText = JSON.stringify(jsonObj, null, 2);
            } catch (e) {
                // Not JSON, just display as is
            }
            responseBodyEl.textContent = responseText;
            responseViewerEl.classList.remove('hidden');

            if (response.error || !response.isSuccess) {
                let errorMessage = response.error || 'Large file upload failed. See response for details.';
                if (response.status === 413) {
                    errorMessage = `📁 File too large (${fileSizeMB}MB) for this webhook endpoint. Try a smaller file or different endpoint.`;
                } else if (response.error && response.error.includes('timeout')) {
                    errorMessage = `⏱️ Large file upload timed out (${fileSizeMB}MB). The file may be too large or connection too slow.`;
                } else if (response.error && response.error.includes('network')) {
                    errorMessage = `🌐 Network error during large file upload (${fileSizeMB}MB). Check your connection and try again.`;
                }
                handleDirectUploadComplete(false, errorMessage, response, fileSizeMB);
            } else {
                handleDirectUploadComplete(true, `Large file upload successful (${fileSizeMB}MB)`, response, fileSizeMB);

                // Update use count for successful uploads
                let currentRequest = state.requests.find(r => r.id === state.selectedRequestId);
                if (currentRequest) {
                    if (response.updatedRequest) {
                        const requestIndex = state.requests.findIndex(r => r.id === state.selectedRequestId);
                        if (requestIndex !== -1) {
                            state.requests[requestIndex] = response.updatedRequest;
                        }
                    } else {
                        currentRequest.useCount = (currentRequest.useCount || 0) + 1;
                        currentRequest.lastUsed = Date.now();
                    }
                    saveState();
                    render();
                }
            }
        } else {
            handleDirectUploadComplete(false, 'No response received from server', null, fileSizeMB);
        }
    }

    /**
     * Toggles pin status for a request
     * @function togglePin
     * @param {string} id - Request ID to toggle pin status
     */
    function togglePin(id) {
        const request = state.requests.find(r => r.id === id);
        if (request) {
            request.isPinned = !request.isPinned;
            saveState();
            render();
        }
    }

    /**
     * Creates a duplicate of an existing request
     * @function duplicateRequest
     * @param {string} id - Request ID to duplicate
     */
    function duplicateRequest(id) {
        const originalRequest = state.requests.find(r => r.id === id);
        if (originalRequest) {
            const now = Date.now();
            const duplicatedRequest = {
                ...originalRequest,
                id: `req_${now}`,
                name: `${originalRequest.name} (Copy)`,
                createdAt: now,
                useCount: 0,
                lastUsed: 0,
                isPinned: false
            };
            state.requests.push(duplicatedRequest);
            saveState();
            selectRequest(duplicatedRequest.id);
            showNotification('Request duplicated successfully', 'success', 2000);
        }
    }

    /**
     * Selects a request for viewing/editing
     * @function selectRequest
     * @param {string} id - Request ID to select
     */
    function selectRequest(id) {
        if (state.selectedRequestId !== id) {
            state.selectedRequestId = id;
            chrome.storage.session.set({ lastSelectedId: id });
            const selectedRequest = state.requests.find(r => r.id === id);
            if (selectedRequest) {
                // This is a remnant of the old toggle switch and is no longer needed.
                // The render() function now handles setting the body type dropdown.
            }
            render();
        }
    }

    /**
     * Renders the entire UI based on current state
     * @function render
     * @returns {void}
     */
    function render() {
        const selectedRequest = state.requests.find(r => r.id === state.selectedRequestId);

        // Declare bodyType at the top to prevent reference errors
        const bodyType = selectedRequest ? (selectedRequest.bodyType || 'json') : 'json';

        // --- Filter and Sort Requests ---
        const filteredRequests = state.requests.filter(req =>
            req.name.toLowerCase().includes(state.searchQuery.toLowerCase())
        );

        const sortedRequests = [...filteredRequests].sort((a, b) => {
            // Always put pinned items first
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;

            // Then apply the selected sort within each group
            switch (state.sortBy) {
                case 'createdAt':
                    return (b.createdAt || 0) - (a.createdAt || 0);
                case 'useCount':
                    // Sort by use count descending, then by lastUsed descending as secondary sort
                    const useCountDiff = (b.useCount || 0) - (a.useCount || 0);
                    if (useCountDiff !== 0) return useCountDiff;
                    return (b.lastUsed || 0) - (a.lastUsed || 0);
                case 'name':
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        // --- Start: Focus Management ---
        let activeElement = document.activeElement;
        let activeElementState = null;
        if (activeElement) {
            if (activeElement.id === 'search-input') {
                activeElementState = {
                    identifier: 'search-input',
                    selectionStart: activeElement.selectionStart,
                    selectionEnd: activeElement.selectionEnd
                };
            } else if (activeElement.parentElement.matches('.header-item, .simple-body-item')) {
                const parent = activeElement.parentElement;
                const removeBtn = parent.querySelector('.remove-header-btn, .remove-simple-row-btn');
                if (removeBtn && removeBtn.dataset) {
                    const index = removeBtn.dataset.index;
                    const type = parent.className.split('-')[0]; // 'header' or 'simple'
                    const fieldClass = activeElement.className;
                    let field = 'key';
                    if (fieldClass.includes('value')) field = 'value';
                    activeElementState = {
                        identifier: `${type}-${index}-${field}`,
                        selectionStart: activeElement.selectionStart,
                        selectionEnd: activeElement.selectionEnd
                    };
                }
            }
        }
        // --- End: Focus Management ---

        // Render request list
        requestsListEl.innerHTML = '';
        if (sortedRequests.length === 0) {
            const li = document.createElement('li');
            li.textContent = 'No webhooks yet. Add one!';
            li.className = 'empty-state';
            requestsListEl.appendChild(li);
        } else {
            sortedRequests.forEach(req => {
                const li = document.createElement('li');
                li.className = `request-item ${req.id === state.selectedRequestId ? 'selected' : ''}`;
                li.dataset.id = req.id;
                li.onclick = () => selectRequest(req.id);

                const text = document.createElement('span');
                text.textContent = req.name;
                text.className = 'request-item-name';

                const pin = document.createElement('button'); // Changed to button for semantics
                pin.innerHTML = req.isPinned ? '&#9733;' : '&#9734;'; // Star icons
                pin.className = `icon-btn request-item-pin ${req.isPinned ? 'pinned' : ''}`;
                pin.title = req.isPinned ? 'Unpin from right-click context menu' : 'Pin to right-click context menu';
                pin.onclick = (e) => {
                    e.stopPropagation();
                    togglePin(req.id);
                };

                li.appendChild(text);
                li.appendChild(pin);
                requestsListEl.appendChild(li);
            });
        }

        if (!selectedRequest) {
            // TODO: Show an empty state
            return;
        }

        // Render editor panel
        requestNameInput.value = selectedRequest.name;
        urlInput.value = selectedRequest.url;
        httpMethodSelect.value = selectedRequest.method;
        bodyTypeSelect.value = selectedRequest.bodyType || 'json';

        // Render headers ONLY for Raw mode - clear for other modes
        if (bodyType === 'raw') {
            headersContainerEl.innerHTML = '';
            selectedRequest.headers.forEach((header, index) => {
                const div = document.createElement('div');
                div.className = 'header-item';
                // Add a wrapper for the inputs to control flex layout
                div.innerHTML = `
                            <div class="header-inputs">
                                <input type="text" class="header-key" placeholder="Key" value="${header.key}">
                                <div class="value-input-wrapper ${header.value ? 'has-content' : ''}">
                                    <input type="text" class="header-value" placeholder="Value" value="${header.value}">
                                    <button class="icon-btn clear-header-value-btn" data-index="${index}" title="Clear value">
                                        <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button class="icon-btn remove-header-btn" data-index="${index}">-</button>
                        `;
                headersContainerEl.appendChild(div);
            });
        } else {
            // 🔧 FORCE CLEAR headers container for JSON and Form-Data modes
            headersContainerEl.innerHTML = '';
        }

        // Render body editors
        simpleBodyContainerEl.innerHTML = '';
        selectedRequest.simpleBody.forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'simple-body-item';
            div.innerHTML = `
                            <textarea class="simple-body-key" placeholder="Key" rows="1">${item.key}</textarea>
                            <div class="value-input-wrapper ${item.value ? 'has-content' : ''}">
                                <textarea class="simple-body-value" placeholder="Value" rows="1">${item.value}</textarea>
                                <button class="icon-btn clear-value-btn" data-index="${index}" title="Clear value">
                                    <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                    </svg>
                                </button>
                            </div>
                            <button class="icon-btn remove-simple-row-btn" data-index="${index}">-</button>
                        `;
            simpleBodyContainerEl.appendChild(div);
        });

        formDataContainerEl.innerHTML = '';
        if (selectedRequest.formDataBody) {
            selectedRequest.formDataBody.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = 'form-data-item';
                let valueContent;
                if (item.type === 'text') {
                    // Use a textarea for better multi-line editing
                    valueContent = `
                        <div class="value-input-wrapper ${item.value ? 'has-content' : ''}">
                            <textarea class="form-data-value" placeholder="Value" rows="1">${item.value || ''}</textarea>
                            <button class="icon-btn clear-value-btn" data-index="${index}" title="Clear value">
                                <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                </svg>
                            </button>
                        </div>
                    `;
                } else { // type is 'file'
                    if (item.fileName) {
                        // Enhanced file display with icons and file info
                        const safeName = item.fileName;
                        const dotIndex = safeName.lastIndexOf('.');
                        const base = dotIndex > 0 ? safeName.slice(0, dotIndex) : safeName;
                        const ext = dotIndex > 0 ? safeName.slice(dotIndex) : '';
                        const fileIcon = getFileTypeIcon(ext);
                        const fileSize = item.fileSize ? formatFileSize(item.fileSize) : '';
                        const isVideo = isVideoFile(ext);
                        const isImage = isImageFile(ext);
                        const isPlaceholder = item.isPlaceholder || (item.value && item.value.startsWith('[LARGE_FILE_PLACEHOLDER]'));
                        const isFreshUpload = item.isFreshUpload || false;
                        const isLargeFile = item.fileSize && item.fileSize > 10 * 1024 * 1024;

                        valueContent = `
                            <div class="form-data-file-display">
                                ${item.thumbnail ?
                                // For images with thumbnails: Show thumbnail + file details (no emoji needed)
                                `<div class="file-thumbnail"><img src="${item.thumbnail}" alt="${safeName}" title="${safeName}"></div>
                                    <div class="file-info">
                                        <div class="file-details">
                                            <div class="form-data-filename" title="${safeName}">
                                                <span class="filename-start">${base}</span><span class="filename-ext">${ext}</span>
                                            </div>
                                            ${fileSize ? `<div class="file-size">${fileSize}</div>` : ''}
                                            ${isFreshUpload && isLargeFile ? '<div class="file-type-label success">Large File Ready</div>' : ''}
                                            ${isPlaceholder ? '<div class="file-type-label warning">Large File (Re-upload needed)</div>' : ''}
                                        </div>
                                    </div>`
                                :
                                // For non-images or files without thumbnails: Show icon + details
                                `<div class="file-info">
                                        <div class="file-icon ${isVideo ? 'video-file' : ''} ${isImage ? 'image-file' : ''} ${isPlaceholder ? 'placeholder-file' : ''} ${isFreshUpload ? 'fresh-upload' : ''}">
                                            ${isPlaceholder ? '⚠️' : isFreshUpload && isLargeFile ? '🚀' : fileIcon}
                                        </div>
                                        <div class="file-details">
                                            <div class="form-data-filename" title="${safeName}">
                                                <span class="filename-start">${base}</span><span class="filename-ext">${ext}</span>
                                            </div>
                                            ${fileSize ? `<div class="file-size">${fileSize}</div>` : ''}
                                            ${isVideo && !isPlaceholder && !isFreshUpload ? '<div class="file-type-label">Video File</div>' : ''}
                                            ${isImage && !isPlaceholder && !isFreshUpload ? '<div class="file-type-label">Image File</div>' : ''}
                                            ${isFreshUpload && isLargeFile && isVideo ? '<div class="file-type-label success">Video Ready to Send</div>' : ''}
                                            ${isFreshUpload && isLargeFile && !isVideo ? '<div class="file-type-label success">Large File Ready</div>' : ''}
                                            ${isPlaceholder ? '<div class="file-type-label warning">Large File (Re-upload needed)</div>' : ''}
                                        </div>
                                    </div>`
                            }
                                <button class="icon-btn clear-file-btn" data-index="${index}" title="Clear file">✕</button>
                            </div>
                        `;
                    } else {
                        valueContent = `
                            <div class="file-upload-area" data-index="${index}">
                                <div class="upload-content">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">
                                        <span class="upload-main">Choose file or drag & drop</span>
                                        <span class="upload-hint">Supports videos, images, documents, and more</span>
                                    </div>
                                </div>
                                <input type="file" class="form-data-file" data-index="${index}" accept="*/*">
                                <div class="upload-progress hidden">
                                    <div class="progress-bar">
                                        <div class="progress-fill"></div>
                                    </div>
                                    <span class="progress-text">Uploading...</span>
                                </div>
                            </div>
                        `;
                    }
                }

                div.innerHTML = `
                    <div class="form-data-controls">
                        <select class="form-data-type" data-index="${index}">
                            <option value="text" ${item.type === 'text' ? 'selected' : ''}>Text</option>
                            <option value="file" ${item.type === 'file' ? 'selected' : ''}>File</option>
                        </select>
                        <textarea class="form-data-key" placeholder="Key" rows="1">${item.key || ''}</textarea>
                    </div>
                    <div class="form-data-value-wrapper">
                        ${valueContent}
                    </div>
                    <button class="icon-btn remove-form-data-row-btn" data-index="${index}">-</button>
                `;
                formDataContainerEl.appendChild(div);
            });
        }
        bodyInput.value = selectedRequest.body;

        // Highlight active sort option
        sortMenuEl.querySelectorAll('.sort-option').forEach(opt => {
            opt.classList.toggle('active', opt.dataset.value === state.sortBy);
        });

        // This logic has been moved to the oninput handlers to prevent re-expansion on render.

        // --- Start: Restore Focus ---
        if (activeElementState) {
            if (activeElementState.identifier === 'search-input') {
                setTimeout(() => {
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.setSelectionRange(activeElementState.selectionStart, activeElementState.selectionEnd);
                    }
                }, 0);
            } else {
                const [type, index, field] = activeElementState.identifier.split('-');
                const container = type === 'header' ? headersContainerEl : simpleBodyContainerEl;
                const row = container.children[index];
                if (row) {
                    const classToFind = type === 'header' ? `.header-${field}` : `.simple-body-${field}`;
                    const elementToFocus = row.querySelector(classToFind);
                    if (elementToFocus) {
                        elementToFocus.focus();
                        // Restore the exact cursor position
                        if (typeof elementToFocus.setSelectionRange === 'function') {
                            elementToFocus.setSelectionRange(activeElementState.selectionStart, activeElementState.selectionEnd);
                        }
                    }
                }
            }
        }
        // --- End: Restore Focus ---
        else {
            // If no specific element was focused, use a timeout to run this *after*
            // the browser's default focus behavior has finished. This is a more
            // aggressive way to prevent the focus from jumping to the delete button.
            setTimeout(() => {
                const editor = document.getElementById('request-editor');
                if (editor) {
                    editor.focus();
                }
                // As a final failsafe, explicitly blur the delete button.
                if (deleteRequestBtn) {
                    deleteRequestBtn.blur();
                }
            }, 0);
        }

        // --- Final UI State Rendering ---
        // bodyType is already declared at the top of the function



        // Render settings
        extremeCleanCheckbox.checked = selectedRequest.extremeClean || false;
        document.getElementById('keep-data-checkbox').checked = selectedRequest.keepData || false;

        // Show/hide body editors with contamination protection
        simpleBodyEditorEl.classList.toggle('hidden', bodyType !== 'json');
        advancedBodyEditorEl.classList.toggle('hidden', bodyType !== 'raw');
        formDataEditorEl.classList.toggle('hidden', bodyType !== 'form-data');

        // 🛡️ CRITICAL FIX: Prevent contaminated data display in form-data mode
        if (bodyType === 'form-data') {
            // Force hide any contaminated JSON editor content
            simpleBodyEditorEl.classList.add('hidden');
            advancedBodyEditorEl.classList.add('hidden');

            // Clear any visible contaminated simpleBody data from UI
            if (selectedRequest.simpleBody && selectedRequest.simpleBody.length > 0) {
                simpleBodyContainerEl.innerHTML = ''; // Clear contaminated UI
            }
        }

        // Hide "Clean JSON" option for Form-Data mode
        const cleanJsonWrapper = extremeCleanCheckbox.parentElement;
        if (bodyType === 'form-data') {
            cleanJsonWrapper.style.display = 'none';
        } else {
            cleanJsonWrapper.style.display = '';
        }

        // Hide Method and Headers sections for JSON and Form-Data modes
        // ONLY show advanced options (Method + Headers) for 'raw' mode
        if (bodyType === 'raw') {
            advancedOptionsEl.classList.remove('hidden');
            advancedOptionsEl.style.display = ''; // Ensure it's visible

            // Show child elements when in Raw mode
            const methodGroup = document.querySelector('#advanced-options .form-group');
            const headersSection = document.querySelector('#advanced-options .headers-section');
            if (methodGroup) {
                methodGroup.style.display = '';
            }
            if (headersSection) {
                headersSection.style.display = '';
            }
        } else {
            // Hide Method and Headers for JSON and Form-Data modes
            advancedOptionsEl.classList.add('hidden');
            advancedOptionsEl.style.display = 'none'; // Force hide with inline style

            // Directly hide child elements if parent hiding fails
            const methodGroup = document.querySelector('#advanced-options .form-group');
            const headersSection = document.querySelector('#advanced-options .headers-section');
            if (methodGroup) {
                methodGroup.style.display = 'none';
            }
            if (headersSection) {
                headersSection.style.display = 'none';
            }
        }
    }

    function flattenObject(obj, parent = '', res = {}) {
        for (let key in obj) {
            let propName = parent ? parent + '.' + key : key;
            if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                flattenObject(obj[key], propName, res);
            } else {
                res[propName] = obj[key];
            }
        }
        return res;
    }

    // Update the state in memory and schedule a save
    function updateState(field, value, index = null, shouldRender = true) {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (!request) return;

        const isSwitchingBodyType = field === 'bodyType';

        if (field.includes('.')) {
            const [parent, child] = field.split('.');
            request[parent][index][child] = value;
        } else {
            request[field] = value;
        }

        // When switching body types, intelligently translate the body AND update headers
        if (isSwitchingBodyType) {

            const contentTypeHeaderIndex = request.headers.findIndex(h => h.key.toLowerCase() === 'content-type');

            if (value === 'form-data') {
                // If we find a Content-Type header, remove it, as the browser must set it.
                if (contentTypeHeaderIndex > -1) {
                    request.headers.splice(contentTypeHeaderIndex, 1);
                }
            } else { // For 'json' and 'raw'
                if (contentTypeHeaderIndex > -1) {
                    request.headers[contentTypeHeaderIndex].value = 'application/json';
                } else {
                    // If no content-type header exists, add it.
                    request.headers.push({ key: 'Content-Type', value: 'application/json' });
                }
            }

            // --- Handle Body Translation ---
            if (value === 'form-data' && (!request.formDataBody || request.formDataBody.length === 0)) {
                // If switching to an empty form-data, create a default file row
                request.formDataBody = [{ type: 'file', key: 'file', value: '' }];

                // Clear contaminated simpleBody data when switching to form-data
                if (request.simpleBody && request.simpleBody.length > 0) {
                    // Clear contaminated data that was causing "post" and "header" to appear
                    request.simpleBody = [];
                }
            } else if (value === 'form-data' && request.formDataBody && request.formDataBody.length > 0) {
                // Even when formDataBody exists, ensure simpleBody contamination is cleared
                if (request.simpleBody && request.simpleBody.length > 0) {
                    request.simpleBody = [];
                }
            } else if (value === 'raw') { // Switching to Raw Text
                // Check if there's existing JSON data to convert
                const hasJsonData = request.simpleBody.some(item => item.key.trim() || item.value.trim());
                if (hasJsonData) {
                    // Stringify the simple body to create the raw body
                    const simpleBodyObject = request.simpleBody.reduce((obj, item) => {
                        if (item.key) obj[item.key] = item.value;
                        return obj;
                    }, {});
                    request.body = JSON.stringify(unflattenObject(simpleBodyObject), null, 2);
                    // Removed notification: 'JSON data converted to Raw Text format'
                } else if (!request.body.trim()) {
                    // If no data exists, provide a default empty JSON structure
                    request.body = '{\n\n}';
                }
            } else if (value === 'json') { // Switching to JSON (Key-Value)
                console.log('🔄 Switching to JSON (Key-Value) mode');

                // 🛡️ CRITICAL CLEANUP: Clear any contaminated simpleBody when switching to JSON
                if (request.simpleBody && request.simpleBody.length > 0) {
                    const isContaminated = request.simpleBody.some(item => {
                        if (!item || !item.key) return false;
                        const key = item.key.toLowerCase();
                        return key.includes('post') || key.includes('method') || key.includes('header') ||
                            key.includes('url') || key.includes('get') || key.includes('put') ||
                            key.includes('delete') || key.includes('content-type') || key.includes('authorization') ||
                            key.includes('code') || key === 'endpoint' || key === 'webhook' || key === 'api';
                    });

                    if (isContaminated) {
                        request.simpleBody = [];
                    }
                }

                // SAFETY: Ensure clean simpleBody initialization
                if (!request.simpleBody || !Array.isArray(request.simpleBody) || request.simpleBody.length === 0) {
                    request.simpleBody = [{ key: '', value: '' }];
                }

                // If the advanced body has content, try to parse it.
                if (request.body.trim()) {
                    try {
                        const repairedJSON = jsonRepair(request.body);
                        const parsedBody = JSON.parse(repairedJSON);
                        const flattened = flattenObject(parsedBody);
                        request.simpleBody = Object.entries(flattened).map(([key, value]) => ({
                            key,
                            value: String(value)
                        }));
                        // Removed notification: 'Raw Text converted to JSON Key-Value format'
                    } catch (e) {
                        // If parsing fails, show an error but still switch the view
                        showNotification('Invalid JSON in raw text body. Could not parse. Starting with empty fields.', 'error', 3000);
                        request.simpleBody = [{ key: '', value: '' }]; // Reset to a blank state
                    }
                } else {
                    // If raw body is empty, just ensure simple body isn't null
                    if (!request.simpleBody || request.simpleBody.length === 0) {
                        request.simpleBody = [{ key: '', value: '' }];
                    }
                }
            }
            render();
        } else if (shouldRender) {
            render(); // Re-render on other state changes
        }

        // Only save state for specific operations that need it immediately
        if (isSwitchingBodyType || shouldRender) {
            saveState();
        }
    }

    /**
     * Checks Chrome storage usage and available space
     * @async
     * @function checkStorageUsage
     * @returns {Promise<{used: number, available: number, usedMB: number, availableMB: number}>}
     */
    async function checkStorageUsage() {
        try {
            const bytesInUse = await chrome.storage.local.getBytesInUse();
            const maxBytes = chrome.storage.local.QUOTA_BYTES || 10 * 1024 * 1024; // 10MB default
            const available = maxBytes - bytesInUse;

            return {
                used: bytesInUse,
                available: available,
                usedMB: bytesInUse / (1024 * 1024),
                availableMB: available / (1024 * 1024)
            };
        } catch (error) {
            return { used: 0, available: 0, usedMB: 0, availableMB: 0 };
        }
    }

    /**
     * Emergency storage cleanup - removes ALL temporary files regardless of age
     * @async
     * @function emergencyStorageCleanup
     * @returns {Promise<{cleaned: number, freedMB: number, success: boolean}>}
     */
    async function emergencyStorageCleanup() {
        try {
            // Get all storage items
            const allItems = await chrome.storage.local.get(null);
            const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
            const uploadStateKeys = Object.keys(allItems).filter(key => key === 'uploadState');

            let cleanedCount = 0;
            let estimatedFreedBytes = 0;

            // Remove ALL temp files (regardless of age)
            for (const key of tempFileKeys) {
                try {
                    const item = allItems[key];
                    if (item && Array.isArray(item)) {
                        // Estimate size of data being removed
                        estimatedFreedBytes += JSON.stringify(item).length;
                    }
                    await chrome.storage.local.remove(key);
                    cleanedCount++;
                } catch (removeError) {
                    // Failed to remove temp file
                }
            }

            // Clear any stale upload states
            for (const key of uploadStateKeys) {
                try {
                    await chrome.storage.local.remove(key);
                } catch (removeError) {
                    // Failed to clear upload state
                }
            }

            // Also clear any potential orphaned data
            const suspiciousKeys = Object.keys(allItems).filter(key =>
                key.includes('temp_') ||
                key.includes('upload_') ||
                (key.length > 20 && !['requests', 'settings', 'theme', 'sortBy', 'panelLeftWidth', 'fullScreenMode'].includes(key))
            );

            for (const key of suspiciousKeys) {
                try {
                    await chrome.storage.local.remove(key);
                    cleanedCount++;
                } catch (removeError) {
                    // Failed to remove suspicious key
                }
            }

            const freedMB = estimatedFreedBytes / (1024 * 1024);

            return {
                cleaned: cleanedCount,
                freedMB: freedMB,
                success: true
            };
        } catch (error) {
            return {
                cleaned: 0,
                freedMB: 0,
                success: false,
                error: error.message
            };
        }
    }


    /**
     * Saves current state to Chrome storage with quota management
     * @function saveState
     * @param {boolean} [showNotificationFlag=false] - Whether to show save notification
     */
    async function saveState(showNotificationFlag = false) {
        try {

            // Create a safe copy of state for serialization
            const safeState = {
                requests: state.requests.map(request => {
                    const safeRequest = { ...request };

                    // Handle form-data files safely
                    if (safeRequest.formDataBody) {
                        safeRequest.formDataBody = safeRequest.formDataBody.map(item => {
                            const safeItem = { ...item };

                            // Check if the file data is too large for Chrome storage
                            if (safeItem.value && typeof safeItem.value === 'string' && safeItem.value.startsWith('data:')) {
                                const sizeInBytes = safeItem.value.length;
                                const sizeInMB = sizeInBytes / (1024 * 1024);

                                // Chrome storage has limits, warn if file is very large
                                if (sizeInMB > 5) {
                                    // Large file detected - may affect performance
                                }

                                // If extremely large (>10MB), check if it's a fresh upload
                                if (sizeInMB > 10) {
                                    if (safeItem.isFreshUpload) {
                                        // Fresh upload - skip storage completely, keep in memory only
                                        return null; // Remove from storage copy
                                    } else {
                                        // Old large file - convert to placeholder
                                        safeItem.value = `[LARGE_FILE_PLACEHOLDER]_${safeItem.fileName}_${safeItem.fileSize}`;
                                        safeItem.isPlaceholder = true;
                                    }
                                }
                            }

                            return safeItem;
                        }).filter(item => item !== null); // Remove null entries (fresh uploads)
                    }

                    return safeRequest;
                }),
                settings: state.settings
            };

            // Estimate data size
            const dataString = JSON.stringify(safeState);
            const dataSizeInMB = dataString.length / (1024 * 1024);

            if (dataSizeInMB > 8) { // Chrome storage limit is around 10MB for local storage
                showNotification('Warning: Data size is large. Consider removing large files to improve performance.', 'warning', 5000);
            }

            // Save the state to local storage with quota error handling
            try {
                await chrome.storage.local.set(safeState);
                if (showNotificationFlag) {
                    showNotification('Request Saved!', 'success');
                }
            } catch (saveError) {
                if (saveError.message && saveError.message.includes('quota')) {
                    const recovered = await handleStorageQuotaExceeded(saveError);
                    if (recovered) {
                        // Retry save after cleanup
                        await chrome.storage.local.set(safeState);
                        showNotification('Request saved after storage cleanup!', 'success');
                    } else {
                        showNotification('Failed to save: Storage quota exceeded. Please remove large files.', 'error', 8000);
                    }
                } else {
                    throw saveError; // Re-throw non-quota errors
                }
            }
        } catch (error) {
            if (error.message && error.message.includes('quota')) {
                showNotification('Storage quota exceeded. Please remove large files or clear extension data.', 'error', 8000);
            } else {
                showNotification('Failed to save data: ' + error.message, 'error', 5000);
            }
        }
    }

    let notificationTimeout = null;
    const notificationMessageEl = document.getElementById('notification-message');
    const notificationActionsEl = document.getElementById('notification-actions');

    function showNotification(message, type = 'success', duration = 3000, actions = []) {
        clearTimeout(notificationTimeout);

        notificationMessageEl.textContent = message;
        notificationBarEl.className = `notification-bar ${type}`;

        // Clear previous actions
        notificationActionsEl.innerHTML = '';

        // Add new action buttons
        if (actions.length > 0) {
            const actionsContainer = document.createElement('div');
            actions.forEach(action => {
                const button = document.createElement('button');
                button.textContent = action.text;
                button.onclick = () => {
                    action.onClick();
                    hideNotification();
                };
                actionsContainer.appendChild(button);
            });
            notificationActionsEl.appendChild(actionsContainer);
        }

        // Show notification with smooth animation
        notificationBarEl.style.opacity = '1';
        notificationBarEl.style.visibility = 'visible';
        notificationBarEl.style.transform = 'translateX(-50%) translateY(0)';

        // Auto-hide if duration is set
        if (duration) {
            notificationTimeout = setTimeout(() => {
                hideNotification();
            }, duration);
        }
    }

    function hideNotification() {
        notificationBarEl.style.opacity = '0';
        notificationBarEl.style.visibility = 'hidden';
        notificationBarEl.style.transform = 'translateX(-50%) translateY(-20px)';
    }

    function clearValidation() {
        document.querySelectorAll('.invalid-field').forEach(el => el.classList.remove('invalid-field'));
    }

    // --- Event Listeners ---
    const keepDataCheckbox = document.getElementById('keep-data-checkbox');
    const keepDataLabel = document.querySelector('label[for="keep-data-checkbox"]');
    keepDataLabel.textContent = 'Keep data 💾';

    const cleanJsonLabel = document.querySelector('label[for="extreme-clean-checkbox"]');
    cleanJsonLabel.textContent = 'Clean JSON 🧹';

    keepDataCheckbox.onchange = (e) => {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (request) {
            request.keepData = e.target.checked;
            saveState();
            showNotification(`Keep Data ${e.target.checked ? 'Enabled' : 'Disabled'}`, 'success');
        }
    };

    extremeCleanCheckbox.onchange = (e) => {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (request) {
            request.extremeClean = e.target.checked;
            saveState();
            showNotification(`Clean JSON ${e.target.checked ? 'Enabled' : 'Disabled'}`, 'success');
        }
    };
    searchInputEl.oninput = (e) => {
        state.searchQuery = e.target.value;
        render();
    };
    requestNameInput.addEventListener('focus', (e) => {
        originalRequestName = e.target.value;
        saveNameBtn.classList.add('hidden'); // Ensure it's hidden on focus
    });

    requestNameInput.addEventListener('input', (e) => {
        if (e.target.value !== originalRequestName) {
            saveNameBtn.classList.remove('hidden');
        } else {
            saveNameBtn.classList.add('hidden');
        }
    });

    requestNameInput.addEventListener('blur', (e) => {
        // If the button is visible, it means there are unsaved changes.
        // A short timeout allows the button click to register before the blur reverts the name.
        setTimeout(() => {
            if (!saveNameBtn.classList.contains('hidden')) {
                e.target.value = originalRequestName;
                saveNameBtn.classList.add('hidden');
            }
        }, 200);
    });

    saveNameBtn.addEventListener('click', () => {
        const newName = requestNameInput.value.trim();
        if (newName) {
            updateState('name', newName, null, true); // Re-render to update the list
            originalRequestName = newName;
            saveNameBtn.classList.add('hidden');
            showNotification('Name saved!', 'success');
        } else {
            requestNameInput.value = originalRequestName;
            showNotification('Name cannot be empty.', 'error');
        }
    });
    urlInput.oninput = (e) => {
        e.target.classList.remove('invalid-field');
        updateState('url', e.target.value);
    };
    httpMethodSelect.onchange = (e) => updateState('method', e.target.value);
    bodyTypeSelect.onchange = (e) => {
        const newBodyType = e.target.value;

        // CRITICAL: Aggressive cleanup when switching to form-data
        if (newBodyType === 'form-data') {
            const request = state.requests.find(r => r.id === state.selectedRequestId);
            if (request) {
                // Force clear simpleBody completely to prevent contamination
                request.simpleBody = [];
                // Ensure formDataBody exists
                if (!request.formDataBody || request.formDataBody.length === 0) {
                    request.formDataBody = [{ type: 'file', key: 'file', value: '' }];
                }
                saveState();
            }
        }

        updateState('bodyType', newBodyType);
    };
    bodyInput.oninput = (e) => updateState('body', e.target.value);


    headersContainerEl.oninput = (e) => {
        if (e.target.matches('.header-key, .header-value')) {
            e.target.classList.remove('invalid-field');
            const removeBtn = e.target.parentElement.querySelector('.remove-header-btn');
            if (!removeBtn) return; // Safety check
            const index = removeBtn.dataset.index;
            const field = e.target.className;
            updateState('headers.' + (field === 'header-key' ? 'key' : 'value'), e.target.value, index);

            // Auto-save data immediately for persistence
            saveState();
        }
    };

    simpleBodyContainerEl.oninput = (e) => {
        // Handle auto-sizing for textareas as the user types
        if (e.target.matches('.simple-body-key, .simple-body-value')) {
            e.target.style.height = 'auto';
            e.target.style.height = (e.target.scrollHeight) + 'px';

            e.target.classList.remove('invalid-field');
            const removeBtn = e.target.parentElement.querySelector('.remove-simple-row-btn');
            if (!removeBtn) return; // Safety check
            const index = removeBtn.dataset.index;
            const field = e.target.className;
            // Update state without triggering a full re-render but ensure it's saved
            updateState('simpleBody.' + (field.includes('key') ? 'key' : 'value'), e.target.value, index, false);

            // Auto-save data immediately for persistence
            saveState();
        }
    };

    // Expand value textarea on focus
    simpleBodyContainerEl.addEventListener('focusin', (e) => {
        if (e.target.matches('.simple-body-key, .simple-body-value')) {
            e.target.classList.add('expanded');
        }
        if (e.target.matches('.simple-body-value')) {
            const text = e.target.value;
            const charCount = text.length;
            const wordCount = text.trim().split(/\s+/).filter(Boolean).length;
            charCountEl.textContent = charCount;
            wordCountEl.textContent = wordCount;
            valueCharWordCountEl.classList.remove('hidden');
        }
    });

    // Shrink value textarea on blur
    simpleBodyContainerEl.addEventListener('focusout', (e) => {
        if (e.target.matches('.simple-body-key, .simple-body-value')) {
            // Check if the new focused element is the send button. If so, do nothing.
            if (e.relatedTarget === sendBtn) {
                return;
            }
            e.target.classList.remove('expanded');
            // Remove any inline height style set by manual resizing
            e.target.style.height = '';
        }

        if (e.target.matches('.simple-body-value')) {
            valueCharWordCountEl.classList.add('hidden');
        }
    });

    simpleBodyContainerEl.addEventListener('input', (e) => {
        if (e.target.matches('.simple-body-value')) {
            const text = e.target.value;
            const charCount = text.length;
            const wordCount = text.trim().split(/\s+/).filter(Boolean).length;
            charCountEl.textContent = charCount;
            wordCountEl.textContent = wordCount;
        }
    });


    // --- Action Buttons & Dynamic Rows ---
    addHeaderBtn.onclick = () => {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (request) {
            request.headers.push({ key: '', value: '' });
            saveState();
            render();
        }
    };

    addSimpleRowBtn.onclick = () => {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (request) {
            request.simpleBody.push({ key: '', value: '' });
            saveState();
            render();
        }
    };

    addFormDataRowBtn.onclick = () => {
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (request) {
            if (!request.formDataBody) {
                request.formDataBody = [];
            }
            // Default to 'file' when adding a new row in the form-data section.
            request.formDataBody.push({ type: 'file', key: '', value: '' });
            saveState();
            render();
        }
    };

    function handleFieldDelete(type, e) {
        if (!e.target.matches(`.remove-${type}-btn`)) return;

        if (!e.target.dataset) return; // Safety check
        const index = parseInt(e.target.dataset.index, 10);
        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (!request) return;

        clearTimeout(deleteFieldTimeout);

        if (deleteFieldConfirmState.type === type && deleteFieldConfirmState.index === index) {
            // This is the second click, confirm delete
            if (type === 'header') {
                request.headers.splice(index, 1);
            } else if (type === 'simple-row') {
                request.simpleBody.splice(index, 1);
            } else if (type === 'form-data-row') {
                request.formDataBody.splice(index, 1);
            }
            deleteFieldConfirmState = { type: null, index: null };
            saveState();
            render();
        } else {
            // This is the first click, enter confirmation state
            // If another button is already in confirm state, reset it first without a full render
            if (deleteFieldConfirmState.type) {
                let container;
                if (deleteFieldConfirmState.type === 'header') {
                    container = headersContainerEl;
                } else if (deleteFieldConfirmState.type === 'simple-row') {
                    container = simpleBodyContainerEl;
                } else {
                    container = formDataContainerEl;
                }
                const oldButton = container.querySelector(`.remove-${deleteFieldConfirmState.type}-btn[data-index="${deleteFieldConfirmState.index}"]`);
                if (oldButton) {
                    oldButton.innerHTML = '-';
                    oldButton.style.backgroundColor = '';
                }
            }

            deleteFieldConfirmState = { type, index };

            const button = e.target;
            button.innerHTML = '&#10003;'; // Checkmark
            button.style.backgroundColor = 'var(--success-color)';

            deleteFieldTimeout = setTimeout(() => {
                button.innerHTML = '-';
                button.style.backgroundColor = ''; // Revert to CSS color
                deleteFieldConfirmState = { type: null, index: null };
            }, 3000);
        }
    }

    // REMOVED: Document-level click handler that was interfering with send button
    // Clear buttons will be handled by their specific container handlers instead

    /**
     * Helper function to handle clearing field values and maintaining expanded state
     * @function handleFieldClear
     * @param {HTMLElement} wrapper - The wrapper element containing the field
     * @param {HTMLElement} field - The input/textarea field to clear
     * @param {string} stateKey - The state key to update
     * @param {number} index - The index of the field
     * @param {string} message - Success message to show
     */
    function handleFieldClear(wrapper, field, stateKey, index, message) {
        const wasExpanded = field.classList.contains('expanded');
        updateState(stateKey, '', index, false);
        field.value = '';
        wrapper.classList.remove('has-content');

        // Maintain expanded state if it was expanded
        if (wasExpanded) {
            setTimeout(() => {
                field.focus();
                field.classList.add('expanded');
            }, 10);
        }

        showNotification(message, 'success', 1500);
    }

    /**
     * Function to toggle clear button visibility based on content
     * @function toggleClearButton
     * @param {HTMLElement} element - The input/textarea element
     */
    function toggleClearButton(element) {
        const wrapper = element.closest('.value-input-wrapper');
        if (wrapper) {
            const hasContent = element.value.trim().length > 0;
            wrapper.classList.toggle('has-content', hasContent);
        }
    }

    // Add input event listeners to manage clear button visibility
    document.addEventListener('input', (e) => {
        if (e.target.matches('.simple-body-value, .header-value, .form-data-value')) {
            toggleClearButton(e.target);
        }
    });

    // Add focus event listeners to ensure clear buttons appear when focusing on fields with content
    document.addEventListener('focus', (e) => {
        if (e.target.matches('.simple-body-value, .header-value, .form-data-value')) {
            toggleClearButton(e.target);
        }
    }, true);

    headersContainerEl.onclick = (e) => {
        // Handle header clear buttons
        const clearBtn = e.target.closest('.clear-header-value-btn');
        if (clearBtn) {
            e.preventDefault();
            e.stopPropagation();

            if (!clearBtn.dataset) return; // Safety check
            const index = parseInt(clearBtn.dataset.index, 10);
            const wrapper = clearBtn.parentElement;
            const input = wrapper.querySelector('.header-value');

            if (input) {
                handleFieldClear(wrapper, input, 'headers.value', index, 'Header value cleared');
            }
            return;
        }

        // Handle field delete
        handleFieldDelete('header', e);
    };
    simpleBodyContainerEl.onclick = (e) => {
        const clearBtn = e.target.closest('.clear-value-btn');
        if (clearBtn) {
            // Handle clear button directly
            e.preventDefault();
            e.stopPropagation();

            if (!clearBtn.dataset) return; // Safety check
            const index = parseInt(clearBtn.dataset.index, 10);
            const wrapper = clearBtn.parentElement;
            const textarea = wrapper.querySelector('.simple-body-value');

            if (textarea) {
                handleFieldClear(wrapper, textarea, 'simpleBody.value', index, 'Value cleared');
            }
            return;
        } else {
            handleFieldDelete('simple-row', e);
        }
    };

    // Add focus/blur listeners for form-data textareas
    formDataContainerEl.addEventListener('focusin', (e) => {
        if (e.target.matches('.form-data-key, .form-data-value')) {
            e.target.classList.add('expanded');
        }
    });

    formDataContainerEl.addEventListener('focusout', (e) => {
        if (e.target.matches('.form-data-key, .form-data-value')) {
            e.target.classList.remove('expanded');
            e.target.style.height = ''; // Reset manual resize on blur
        }
    });


    formDataContainerEl.onclick = (e) => {
        // Only handle clicks that occur within the form data container
        if (!formDataContainerEl.contains(e.target)) {
            return;
        }

        // Check if the click target is actually a clear file button
        const clearFileBtn = e.target.closest('.clear-file-btn');
        if (clearFileBtn && clearFileBtn.dataset && formDataContainerEl.contains(clearFileBtn)) {
            e.preventDefault();
            e.stopPropagation();

            const index = clearFileBtn.dataset.index;
            const request = state.requests.find(r => r.id === state.selectedRequestId);
            if (request && request.formDataBody[index]) {
                // Clear all file-related data
                request.formDataBody[index].value = '';
                request.formDataBody[index].fileName = '';
                request.formDataBody[index].fileSize = 0;
                request.formDataBody[index].fileType = '';
                request.formDataBody[index].isFreshUpload = false;
                request.formDataBody[index].isPlaceholder = false;
                request.formDataBody[index].thumbnail = null; // Clear thumbnail

                // Save state and re-render
                saveState();
                render();

                // Force button state update after file clear
                setTimeout(() => {
                    resetSendButton();
                }, 100);

                // Removed file clear notifications - UI feedback is sufficient
                // if (wasFreshUpload && wasLargeFile) {
                //     showNotification('🗑️ Large file cleared. Ready for new upload.', 'success', 2000);
                // } else {
                //     showNotification('🗑️ File cleared', 'success', 1500);
                // }
            }
        } else if (e.target.matches('.remove-form-data-row-btn')) {
            handleFieldDelete('form-data-row', e);
        }
    };

    formDataContainerEl.addEventListener('input', (e) => {
        const itemEl = e.target.closest('.form-data-item');
        if (!itemEl) return;

        const removeBtn = itemEl.querySelector('.remove-form-data-row-btn');
        if (!removeBtn) return; // Safety check
        const index = removeBtn.dataset.index;

        // Handle auto-sizing for textareas as the user types
        if (e.target.matches('.form-data-key, .form-data-value')) {
            e.target.style.height = 'auto';
            e.target.style.height = (e.target.scrollHeight) + 'px';
        }

        if (e.target.matches('.form-data-key')) {
            // State update logic
            updateState('formDataBody.key', e.target.value, index, false);
        } else if (e.target.matches('.form-data-value')) {
            // State update logic
            updateState('formDataBody.value', e.target.value, index, false);
        }

        // Auto-save data immediately for persistence
        saveState();
    });

    formDataContainerEl.addEventListener('change', (e) => {
        if (e.target.matches('.form-data-type')) {
            if (!e.target.dataset) return; // Safety check
            const index = e.target.dataset.index;
            const request = state.requests.find(r => r.id === state.selectedRequestId);
            if (request && request.formDataBody[index]) {
                request.formDataBody[index].type = e.target.value;
                if (e.target.value === 'text' && request.formDataBody[index].value === undefined) {
                    request.formDataBody[index].value = '';
                }
                saveState();
                render();
            }
        } else if (e.target.matches('.form-data-file')) {
            if (!e.target.dataset) return; // Safety check
            const index = e.target.dataset.index;
            const request = state.requests.find(r => r.id === state.selectedRequestId);
            if (request && request.formDataBody[index]) {
                const file = e.target.files[0];
                if (file) {
                    // File size validation - Cap at 20MB for practical webhook usage
                    const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
                    if (file.size > MAX_FILE_SIZE) {
                        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
                        showNotification(
                            `❌ File too large: ${fileSizeMB}MB. Maximum supported: 20MB.\n\n💡 Most webhooks support files under 10MB. Consider compressing your file or using cloud storage + URL approach.`,
                            'error',
                            8000
                        );
                        // Clear the file input
                        e.target.value = '';
                        return;
                    }

                    // Show webhook size warning for files over 10MB
                    if (file.size > 10 * 1024 * 1024) {
                        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
                        showNotification(
                            `⚠️ Large file detected: ${fileSizeMB}MB. Most webhooks support files under 10MB. Your upload may fail with services like Make.com (5MB limit).`,
                            'warning',
                            6000
                        );
                    }

                    // Show upload progress
                    const uploadArea = e.target.closest('.file-upload-area');
                    const progressContainer = uploadArea?.querySelector('.upload-progress');
                    const progressFill = uploadArea?.querySelector('.progress-fill');
                    const progressText = uploadArea?.querySelector('.progress-text');

                    if (progressContainer) {
                        progressContainer.classList.remove('hidden');
                        uploadArea.querySelector('.upload-content')?.classList.add('hidden');
                    }

                    const reader = new FileReader();

                    // Progress tracking
                    reader.onprogress = (event) => {
                        if (event.lengthComputable && progressFill && progressText) {
                            const percentComplete = (event.loaded / event.total) * 100;
                            progressFill.style.width = percentComplete + '%';
                            progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
                        }
                    };

                    reader.onload = (event) => {
                        const fileSize = file.size;
                        const fileSizeInMB = fileSize / (1024 * 1024);

                        request.formDataBody[index].value = event.target.result;
                        request.formDataBody[index].fileName = file.name;
                        request.formDataBody[index].fileSize = file.size; // Store file size
                        request.formDataBody[index].fileType = file.type; // Store MIME type

                        // Generate thumbnail for images
                        const fileExtension = file.name.substring(file.name.lastIndexOf('.'));
                        if (isImageFile(fileExtension)) {
                            generateThumbnail(event.target.result, file.type)
                                .then(thumbnail => {
                                    request.formDataBody[index].thumbnail = thumbnail;
                                    // Re-render to show thumbnail
                                    render();
                                })
                                .catch(() => {
                                    // Continue without thumbnail
                                });
                        }

                        // Mark large files as fresh uploads (don't save to storage)
                        if (fileSizeInMB > 10) {
                            request.formDataBody[index].isFreshUpload = true;
                            request.formDataBody[index].isPlaceholder = false;
                        } else {
                            request.formDataBody[index].isFreshUpload = false;
                            request.formDataBody[index].isPlaceholder = false;
                        }

                        if (progressText) {
                            progressText.textContent = 'Upload complete!';
                        }

                        // Small delay to show completion before re-rendering
                        setTimeout(() => {
                            // Only save to storage if it's not a large file
                            if (fileSizeInMB <= 10) {
                                saveState();
                            }
                            render();

                            // Show enhanced success notification
                            const fileIcon = getFileTypeIcon(file.name.substring(file.name.lastIndexOf('.')));
                            const fileSize = formatFileSize(file.size);

                            let message = `${fileIcon} File uploaded: ${file.name} (${fileSize})`;
                            if (fileSizeInMB > 10) {
                                message += ' - Ready to send!';
                            }

                            // Removed file upload notification - UI already shows upload status
                            // showNotification(message, 'success', 3000);
                        }, 500);
                    };

                    reader.onerror = () => {
                        if (progressText) {
                            progressText.textContent = 'Upload failed!';
                        }
                        showNotification('File upload failed. Please try again.', 'error', 3000);

                        // Reset the upload area after a delay
                        setTimeout(() => {
                            if (progressContainer) {
                                progressContainer.classList.add('hidden');
                                uploadArea.querySelector('.upload-content')?.classList.remove('hidden');
                            }
                        }, 2000);
                    };

                    reader.readAsDataURL(file);
                }
            }
        }
    });

    // Add drag and drop functionality for file uploads
    document.addEventListener('dragover', (e) => {
        e.preventDefault();
        const uploadArea = e.target.closest('.file-upload-area');
        if (uploadArea) {
            uploadArea.classList.add('drag-over');
        }
    });

    document.addEventListener('dragleave', (e) => {
        e.preventDefault();
        const uploadArea = e.target.closest('.file-upload-area');
        if (uploadArea && !uploadArea.contains(e.relatedTarget)) {
            uploadArea.classList.remove('drag-over');
        }
    });

    document.addEventListener('drop', (e) => {
        e.preventDefault();
        const uploadArea = e.target.closest('.file-upload-area');
        if (uploadArea) {
            uploadArea.classList.remove('drag-over');

            const index = uploadArea.dataset.index;
            const files = e.dataTransfer.files;

            if (files.length > 0 && index) {
                const file = files[0];
                const request = state.requests.find(r => r.id === state.selectedRequestId);

                if (request && request.formDataBody[index]) {
                    // Simulate file input change for consistent handling
                    const fileInput = uploadArea.querySelector('.form-data-file');
                    if (fileInput) {
                        // Create a new FileList-like object
                        const dt = new DataTransfer();
                        dt.items.add(file);
                        fileInput.files = dt.files;

                        // Trigger the change event
                        const changeEvent = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(changeEvent);
                    }
                }
            }
        }
    });

    addNewRequestBtn.onclick = () => {
        const now = Date.now();
        const newRequest = {
            id: `req_${now}`,
            name: 'New Request',
            bodyType: 'json',
            method: 'POST',
            url: '',
            headers: [{ key: 'Content-Type', value: 'application/json' }],
            simpleBody: [{ key: '', value: '' }],
            formDataBody: [],
            body: '',
            createdAt: now,
            useCount: 0,
            lastUsed: 0,
            keepData: false, // Per-request setting
            extremeClean: false, // Per-request setting
        };
        state.requests.push(newRequest);
        saveState();
        selectRequest(newRequest.id);

        // After rendering the new request, immediately highlight required fields.
        // Using requestAnimationFrame ensures this runs after the DOM is painted.
        requestAnimationFrame(() => {
            clearValidation();
            urlInput.classList.add('invalid-field');
            const firstKeyInput = simpleBodyContainerEl.querySelector('.simple-body-key');
            if (firstKeyInput) {
                firstKeyInput.classList.add('invalid-field');
            }
            showNotification('Please fill in the highlighted fields.', 'info');
            urlInput.focus();
        });
    };

    deleteRequestBtn.onclick = () => {
        if (!state.selectedRequestId) return;

        const actions = [
            {
                text: 'Confirm',
                onClick: () => {
                    state.requests = state.requests.filter(r => r.id !== state.selectedRequestId);
                    const newIdToSelect = state.requests.length > 0 ? state.requests[0].id : null;
                    state.selectedRequestId = null; // Deselect first
                    saveState();
                    selectRequest(newIdToSelect); // Then select the new one
                    showNotification('Request deleted.', 'success');
                }
            },
            {
                text: 'Cancel',
                onClick: () => { /* Do nothing */ }
            }
        ];
        showNotification('Are you sure you want to delete this request?', 'confirm', null, actions);
    };

    sortBtn.onclick = (e) => {
        e.stopPropagation(); // Prevent the document click listener from firing immediately
        sortMenuEl.classList.toggle('hidden');
    };

    themeToggleBtn.onclick = () => {
        toggleTheme();
    };

    // Add fullscreen toggle functionality
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    if (fullscreenBtn) {
        fullscreenBtn.onclick = () => {
            document.body.classList.toggle('fullscreen-mode');
        };
    }

    clearRawBodyBtn.onclick = () => {
        bodyInput.value = '';
        updateState('body', '');
        showNotification('Raw body cleared', 'success', 1500);
    };

    sortMenuEl.onclick = (e) => {
        if (e.target.matches('.sort-option')) {
            const sortBy = e.target.dataset.value;
            state.sortBy = sortBy;
            chrome.storage.session.set({ sortBy: sortBy });
            sortMenuEl.classList.add('hidden');
            render();
        }
    };

    // Hide sort menu if clicking elsewhere
    document.addEventListener('click', (e) => {
        if (!sortMenuEl.contains(e.target) && !sortBtn.contains(e.target)) {
            sortMenuEl.classList.add('hidden');
        }
    });

    sendBtn.onclick = async (e) => {
        // Prevent event bubbling to other handlers
        e.preventDefault();
        e.stopPropagation();

        const request = state.requests.find(r => r.id === state.selectedRequestId);
        if (!request) {
            showNotification('No request selected. Please select or create a request first.', 'error');
            return;
        }

        clearValidation();

        // --- Primary Validation ---
        // Enhanced URL validation
        const isValidUrl = (url) => {
            try {
                const urlObj = new URL(url);
                return ['http:', 'https:'].includes(urlObj.protocol);
            } catch {
                return false;
            }
        };

        if (!request.url || !isValidUrl(request.url)) {
            showNotification('A valid HTTP/HTTPS URL is required.', 'error');
            urlInput.classList.add('invalid-field');
            urlInput.focus();
            return;
        }

        // --- Body Validation (Construction is now done in the background script) ---
        if (request.bodyType === 'raw') {
            if (['POST', 'PUT'].includes(request.method) && !request.body.trim()) {
                showNotification('Body cannot be empty for POST/PUT requests in Raw mode.', 'error');
                return;
            }
        } else if (request.bodyType !== 'form-data') { // Simple Mode Validation
            let hasAtLeastOneKey = false;
            let allValuesHaveKeys = true;
            for (const [index, item] of request.simpleBody.entries()) {
                const row = simpleBodyContainerEl.children[index];
                const trimmedKey = (item.key || '').trim();
                const trimmedValue = (item.value || '').trim();

                if (trimmedKey) {
                    hasAtLeastOneKey = true;
                    if (!trimmedValue) {
                        if (row) row.querySelector('.simple-body-value').classList.add('invalid-field');
                        showNotification(`A value is required for the key "${trimmedKey}".`, 'error');
                        return;
                    }
                } else if (trimmedValue) {
                    allValuesHaveKeys = false;
                    if (row) row.querySelector('.simple-body-key').classList.add('invalid-field');
                }
            }
            if (!allValuesHaveKeys) {
                showNotification('A key is required for every value.', 'error');
                return;
            }
            if (!hasAtLeastOneKey) {
                showNotification('At least one Key is required to send a request.', 'error');
                const firstKeyInput = simpleBodyContainerEl.querySelector('.simple-body-key');
                if (firstKeyInput) firstKeyInput.classList.add('invalid-field');
                return;
            }
        }

        // --- UI Feedback for Sending ---
        const hasLargeFiles = request.bodyType === 'form-data' && request.formDataBody?.some(item =>
            item.type === 'file' && item.fileSize && item.fileSize > 5 * 1024 * 1024
        );

        // Calculate total file size for user feedback
        let totalFileSize = 0;
        let largestFileName = '';
        if (request.bodyType === 'form-data' && request.formDataBody) {
            request.formDataBody.forEach(item => {
                if (item.type === 'file' && item.fileSize) {
                    totalFileSize += item.fileSize;
                    if (item.fileSize > 0 && (!largestFileName || item.fileSize > request.formDataBody.find(f => f.fileName === largestFileName)?.fileSize)) {
                        largestFileName = item.fileName;
                    }
                }
            });
        }

        sendBtn.classList.add('loading');
        sendBtn.disabled = true;

        // Enhanced UI feedback based on file size
        const btnText = sendBtn.querySelector('.btn-text');
        if (hasLargeFiles) {
            const fileSizeMB = (totalFileSize / (1024 * 1024)).toFixed(1);
            btnText.innerHTML = `Preparing... <div class="upload-info"><small>📁 ${fileSizeMB}MB total</small></div>`;
            showNotification(`📁 Preparing large file upload (${fileSizeMB}MB). This may take a moment...`, 'info', 4000);
        } else {
            btnText.textContent = 'Sending...';
        }

        // Create message with unique request ID first (to avoid ReferenceError)
        const requestId = Date.now().toString();

        // Store large file data separately to avoid message size limits
        const originalFormDataFiles = [];
        let hasVeryLargeFiles = false;
        const FILE_SIZE_LIMIT = 20 * 1024 * 1024; // 20MB practical limit
        const CHROME_STORAGE_SAFE_LIMIT = 8 * 1024 * 1024; // 8MB safe limit for Chrome storage

        if (request.formDataBody) {
            request.formDataBody.forEach((item, originalIndex) => {
                if (item.type === 'file' && item.value) {
                    const fileSize = item.value.length; // This is the base64 encoded size
                    // Note: Base64 encoding increases size by ~33%, so actual file size is smaller
                    const actualFileSize = Math.floor(fileSize * 0.75); // Estimate actual file size

                    // Reject files over 20MB limit
                    if (fileSize > FILE_SIZE_LIMIT) {
                        const errorMsg = `File ${item.fileName} (${(fileSize / (1024 * 1024)).toFixed(1)}MB) exceeds 20MB limit`;
                        showNotification(`❌ ${errorMsg}. Please compress the file or use a cloud storage + URL approach.`, 'error', 6000);
                        return { error: errorMsg, isSuccess: false };
                    }

                    if (fileSize > CHROME_STORAGE_SAFE_LIMIT) {
                        hasVeryLargeFiles = true;
                    }

                    originalFormDataFiles.push({
                        index: originalIndex,
                        data: item.value,
                        fileName: item.fileName,
                        key: item.key,
                        size: fileSize,
                        actualSize: actualFileSize
                    });
                }
            });

            // Verify that files under 20MB can be handled correctly
            const regularFiles = originalFormDataFiles.filter(f => f.size <= FILE_SIZE_LIMIT);
            const totalRegularFileSize = regularFiles.reduce((sum, f) => sum + f.size, 0);

            if (regularFiles.length > 0 && totalRegularFileSize > CHROME_STORAGE_SAFE_LIMIT * 0.8) {
                showNotification(`⚠️ Large total file size detected. Upload may be slower than usual.`, 'warning', 4000);
            }
        }

        // Handle large files (8-20MB) differently - use enhanced storage method
        if (hasVeryLargeFiles) {
            const veryLargeFileSizeMB = (totalFileSize / (1024 * 1024)).toFixed(1);

            // Enhanced user feedback for very large files
            btnText.innerHTML = `Preparing... <div class="upload-info"><small>🚀 ${veryLargeFileSizeMB}MB - Preparing for upload</small><div class="progress-bar"><div class="progress-fill" style="width: 10%"></div></div></div>`;
            showNotification(`🚀 Large file detected (${veryLargeFileSizeMB}MB) - using enhanced upload method. Please wait...`, 'info', 5000);

            // Process very large files by storing them in chunks to avoid message size limits
            try {
                await handleVeryLargeFileUpload(request, requestId, originalFormDataFiles, veryLargeFileSizeMB);
            } catch (error) {
                handleDirectUploadComplete(false, `Large file upload failed: ${error.message}`, null, veryLargeFileSizeMB);
            }
            return;
        }

        // Save file data to temporary storage if there are files (normal size)
        if (originalFormDataFiles.length > 0) {
            const normalFileSizeMB = (totalFileSize / (1024 * 1024)).toFixed(1);
            // Update UI to show file preparation
            btnText.innerHTML = `Preparing... <div class="upload-info"><small>💾 Storing ${normalFileSizeMB}MB to temp storage</small></div>`;

            // Remove old temp files before uploading new ones
            const cleanedCount = await emergencyStorageCleanup();
            if (cleanedCount.cleaned > 0) {
                showNotification(`🧹 Cleared ${cleanedCount.cleaned} old files (${cleanedCount.freedMB.toFixed(1)}MB) to make space`, 'success', 2000);
            }

            const tempFileKey = `temp_files_${requestId}`;
            try {
                await chrome.storage.local.set({ [tempFileKey]: originalFormDataFiles });

                // Update UI to show storage complete
                btnText.innerHTML = `Ready to send... <div class="upload-info"><small>✅ ${normalFileSizeMB}MB staged</small></div>`;
            } catch (storageError) {
                showNotification('Storage full: Cannot upload large files. Please remove some data and try again.', 'error', 8000);
                sendBtn.classList.remove('loading');
                sendBtn.disabled = false;
                btnText.textContent = 'Send';
                return;
            }
        }

        // Create a completely safe copy that prevents message length errors
        const safeRequest = {
            ...request,
            // Remove file data from the message sent to the background script
            formDataBody: request.formDataBody ? request.formDataBody.map((item, index) => {
                if (item.type === 'file' && item.value) {
                    console.log(`🔧 DEBUG: Mapping file at index ${index}: ${item.fileName} (key: ${item.key})`);
                    return {
                        ...item,
                        value: null, // Nullify file data
                        _isFileRef: true, // Mark as file reference
                        _originalIndex: index // Store original index for restoration
                    };
                }
                return item;
            }) : []
        };

        // --- Send to Background Script ---
        chrome.runtime.sendMessage({
            action: 'executeRequest',
            requestData: safeRequest,
            requestId: requestId,
            _hasLargeFiles: originalFormDataFiles.length > 0
        }, (response) => {
            if (chrome.runtime.lastError) {
                if (!chrome.runtime.lastError.message.includes('port closed')) {
                    showNotification('Communication error. Please try again.', 'error', 3000);
                }
                sendBtn.classList.remove('loading');
                sendBtn.disabled = false;
                return;
            }

            if (response && response.status === 'progress') {
                state.activeUpload = {
                    requestId: response.requestId,
                    isUploading: true,
                    startTime: Date.now(),
                    hasLargeFiles: true,
                    totalSize: totalFileSize
                };
                saveUploadState();
                sendBtn.classList.remove('loading');
                sendBtn.classList.add('sending');

                // Enhanced progress UI with time estimates and cancel button
                const fileSizeMB = (totalFileSize / (1024 * 1024)).toFixed(1);
                btnText.innerHTML = `Uploading... <div class="upload-info"><small>🚀 ${fileSizeMB}MB - Starting...</small><div class="progress-container"><div class="progress-bar"><div class="progress-fill" style="width: 0%"></div></div><span class="progress-percentage">0%</span></div><button class="cancel-upload-btn" onclick="cancelUpload()" title="Cancel Upload">❌</button></div>`;

                const progressInterval = setInterval(() => {
                    chrome.runtime.sendMessage({ action: 'getProgress', requestId: response.requestId }, (progressResponse) => {
                        if (chrome.runtime.lastError) {
                            if (!chrome.runtime.lastError.message.includes('port closed')) {
                                // Progress tracking error
                            }
                            clearInterval(progressInterval);
                            handleUploadComplete(true, 'Upload completed (progress tracking ended)');
                            return;
                        }

                        if (progressResponse && progressResponse.progress) {
                            const progress = progressResponse.progress;
                            const progressSpan = btnText.querySelector('.progress-percentage');
                            const progressBar = btnText.querySelector('.progress-fill');
                            const infoText = btnText.querySelector('.upload-info small');

                            if (progressSpan && progressBar && infoText) {
                                const percentage = progress.progress || 0;
                                progressSpan.textContent = `${percentage}%`;
                                progressBar.style.width = `${percentage}%`;

                                // Calculate time estimates
                                const elapsed = (Date.now() - state.activeUpload.startTime) / 1000;
                                const rate = (percentage / 100) * totalFileSize / elapsed; // bytes per second
                                const rateMBps = (rate / (1024 * 1024)).toFixed(1);

                                let statusText = `🚀 ${fileSizeMB}MB`;
                                if (percentage > 5 && elapsed > 2) {
                                    const remainingBytes = totalFileSize * (1 - percentage / 100);
                                    const estimatedTimeLeft = Math.round(remainingBytes / rate);
                                    if (estimatedTimeLeft > 60) {
                                        statusText += ` - ~${Math.round(estimatedTimeLeft / 60)}min left`;
                                    } else if (estimatedTimeLeft > 0) {
                                        statusText += ` - ~${estimatedTimeLeft}s left`;
                                    }
                                    statusText += ` (${rateMBps}MB/s)`;
                                } else {
                                    statusText += ' - Calculating...';
                                }

                                infoText.textContent = statusText;
                            }

                            if (progress.stage === 'complete') {
                                clearInterval(progressInterval);
                                handleUploadComplete(true, undefined, progress.finalResponse);
                            } else if (progress.stage === 'error') {
                                clearInterval(progressInterval);
                                handleUploadComplete(false, progress.error);
                            }
                        } else {
                            clearInterval(progressInterval);
                            handleUploadComplete(true, 'Upload finished.');
                        }
                    });
                }, 500);
            } else if (response) {
                sendBtn.classList.remove('loading');
                sendBtn.disabled = false;

                responseStatusEl.textContent = `Status: ${response.status}`;
                responseStatusEl.className = response.isSuccess ? 'status-success' : 'status-error';

                let responseText = response.body || response.error || 'No response body.';
                try {
                    const jsonObj = JSON.parse(responseText);
                    responseText = JSON.stringify(jsonObj, null, 2);
                } catch (e) {
                    // Not JSON, just display as is
                }
                responseBodyEl.textContent = responseText;
                responseViewerEl.classList.remove('hidden');

                if (response.error || !response.isSuccess) {
                    sendBtn.classList.add('error');
                    sendBtn.style.backgroundColor = 'var(--error-color)';
                    setTimeout(() => {
                        sendBtn.classList.remove('error');
                        sendBtn.style.backgroundColor = '';
                    }, 2000);

                    let errorMessage = response.error || 'Request failed. See response for details.';
                    if (response.status === 413) {
                        errorMessage = '📁 File too large for this webhook endpoint. Try a smaller file or different endpoint.';
                    } else if (response.error && response.error.includes('timeout')) {
                        errorMessage = '⏱️ Large file upload timed out. The file may be too large or connection too slow.';
                    } else if (response.error && response.error.includes('network')) {
                        errorMessage = '🌐 Network error during large file upload. Check your connection and try again.';
                    }

                    showNotification(errorMessage, 'error');
                } else {
                    showNotification('🚀 Request sent successfully!', 'success');
                }

                let currentRequest = state.requests.find(r => r.id === state.selectedRequestId);
                if (currentRequest) {
                    if (response.updatedRequest) {
                        const requestIndex = state.requests.findIndex(r => r.id === state.selectedRequestId);
                        if (requestIndex !== -1) {
                            state.requests[requestIndex] = response.updatedRequest;
                        }
                    } else {
                        currentRequest.useCount = (currentRequest.useCount || 0) + 1;
                        currentRequest.lastUsed = Date.now();
                    }
                    saveState();
                    render();
                }
            } else {
                sendBtn.classList.remove('loading');
                sendBtn.disabled = false;
                showNotification('An unexpected error occurred. No response from background.', 'error');
            }
        });
    };

    testUrlBtn.onclick = async () => {
        const url = urlInput.value;
        if (!url) {
            showNotification('A valid URL is required to run a test.', 'error');
            urlInput.classList.add('invalid-field');
            return;
        }

        showNotification('Testing URL...', 'info');
        chrome.runtime.sendMessage({ action: 'testUrl', url }, (response) => {
            if (chrome.runtime.lastError) {
                showNotification(`Test failed: ${chrome.runtime.lastError.message}`, 'error');
                return;
            }
            if (response.error) {
                showNotification(`Test failed: ${response.error}`, 'error');
            } else {
                const message = `Test successful! Status: ${response.status} (${response.statusText})`;
                const type = response.ok ? 'success' : 'error';
                showNotification(message, type);
            }
        });
    };

    exportBtn.onclick = () => {
        if (state.requests.length === 0) {
            showNotification('Nothing to export.', 'error');
            return;
        }
        const dataStr = JSON.stringify(state.requests, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'webhooks.json';
        a.click();
        URL.revokeObjectURL(url);
        showNotification('Requests exported!', 'success');
    };

    importFileEl.onchange = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedRequests = JSON.parse(event.target.result);
                if (!Array.isArray(importedRequests)) {
                    throw new Error('Imported file is not a valid array.');
                }

                // Basic validation of imported data
                const validRequests = importedRequests.filter(req =>
                    req.id && req.name && req.url && req.method &&
                    Array.isArray(req.headers) && Array.isArray(req.simpleBody)
                );

                if (validRequests.length !== importedRequests.length) {
                    throw new Error('Imported data has an invalid structure or missing fields.');
                }

                // Merge with existing requests, avoiding duplicates
                const existingIds = new Set(state.requests.map(r => r.id));
                const newRequests = validRequests.filter(r => !existingIds.has(r.id));

                state.requests = [...state.requests, ...newRequests];
                saveState();
                render();
                showNotification(`Imported ${newRequests.length} new requests!`, 'success');
            } catch (err) {
                showNotification(`Import failed: ${err.message}`, 'error');
            } finally {
                // Reset file input
                importFileEl.value = '';
            }
        };
        reader.readAsText(file);
    };

    // --- Initialization ---
    initializeTheme();
    initializeNotificationBar();

    // CRITICAL: Reset send button on load to clear any stuck states
    resetSendButton();

    loadInitialState();
});
