@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* General utility classes */
.hidden {
    display: none !important;
}

*,
*::before,
*::after {
    box-sizing: border-box;
}

:root {
    --primary-color: #4a90e2;
    /* A softer, more modern blue */
    --secondary-color: #50e3c2;
    /* A vibrant teal for accents */
    --background-color: #f4f7f9;
    /* A cooler, lighter gray */
    --panel-background: #ffffff;
    --input-background: #fdfdff;
    --border-color: #dfe3e6;
    --text-color: #2c3e50;
    /* A darker, softer black */
    --light-text-color: #7f8c8d;
    /* A muted gray for less important text */
    --error-color: #e74c3c;
    /* A softer red */
    --success-color: #2ecc71;
    /* A fresh, modern green */

    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Dark theme variables */
[data-theme="dark"] {
    --primary-color: #5aa3f0;
    --secondary-color: #64f4d2;
    --background-color: #1a1a1a;
    --panel-background: #2d2d2d;
    --input-background: #3a3a3a;
    --border-color: #444444;
    --text-color: #e0e0e0;
    --light-text-color: #b0b0b0;
    --error-color: #ff6b6b;
    --success-color: #51cf66;
}


body {
    font-family: var(--font-family);
    margin: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    width: 700px;
    /* Increased width for better usability */
    height: 550px;
    /* Increased height for more content */
    /* Improved size for better user experience */
    transition: background-color 0.3s, color 0.3s;
    overflow: hidden;
    /* Prevent the body itself from scrolling */
}

.notification-bar {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    min-width: 300px;
    max-width: 500px;
    color: white;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* .notification-bar.visible is no longer needed for transform */

#notification-actions div {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

#notification-actions button {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid white;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

#notification-actions button:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

#notification-message {
    font-weight: bold;
}

.notification-bar.success {
    background-color: var(--success-color);
}

.notification-bar.error {
    background-color: var(--error-color);
}

.notification-bar.warning {
    background-color: #f39c12;
    border-color: #e67e22;
}

.notification-bar.info {
    background-color: var(--primary-color);
}

.notification-bar.confirm {
    background-color: var(--success-color);
    color: white;
}

.notification-bar.confirm #notification-actions button {
    background-color: black;
    color: white;
    border: 1px solid black;
    margin-left: 5px;
    border-radius: 8px;
}

.notification-bar.confirm #notification-actions button:hover {
    background-color: #333;
}

.notification-bar.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-20px);
}

/* Enhanced Upload Progress Styles */
.upload-info {
    margin-top: 4px;
    font-size: 11px;
    color: var(--light-text-color);
    line-height: 1.2;
}

.upload-info small {
    display: block;
    margin-bottom: 2px;
    font-weight: 500;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 3px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg,
            var(--success-color) 0%,
            var(--secondary-color) 50%,
            var(--primary-color) 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-percentage {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-color);
    min-width: 28px;
    text-align: right;
}

/* Send button enhanced states */
#send-btn.sending {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    animation: pulse-sending 2s infinite;
}

@keyframes pulse-sending {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }
}

#send-btn.sending .btn-text {
    font-size: 12px;
}

#send-btn.sending .upload-info {
    color: rgba(255, 255, 255, 0.9);
}

#send-btn.sending .progress-bar {
    background-color: rgba(255, 255, 255, 0.2);
}

#send-btn.sending .progress-percentage {
    color: white;
    font-weight: 700;
}

/* Dark theme adjustments for progress UI */
[data-theme="dark"] .upload-info small {
    color: var(--light-text-color);
}

[data-theme="dark"] .progress-bar {
    background-color: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .progress-percentage {
    color: var(--text-color);
}

[data-theme="dark"] #send-btn.sending .upload-info {
    color: rgba(255, 255, 255, 0.95);
}

/* Enhanced notification styles for large files */
.notification-bar.large-file {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-bar.large-file #notification-message {
    font-size: 13px;
    font-weight: 600;
}

.container {
    display: flex;
    height: 100%;
    /* Changed from 100vh to 100% */
    border-radius: 8px;
    overflow: hidden;
}

.panel-left {
    min-width: 180px;
    max-width: 400px;
    width: 220px;
    /* Default width */
    border-right: 1px solid var(--border-color);
    padding: 8px;
    /* Reduced padding */
    background-color: var(--panel-background);
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s, border-color 0.3s;
    flex-shrink: 0;
    /* Prevent shrinking */
}

.resize-handle {
    width: 4px;
    background-color: var(--border-color);
    cursor: col-resize;
    position: relative;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.resize-handle:hover {
    background-color: var(--primary-color);
}

.resize-handle:active {
    background-color: var(--primary-color);
}

/* Full-Screen Mode Styles */
.fullscreen-mode .panel-left {
    display: none;
}

.fullscreen-mode .resize-handle {
    display: none;
}

.fullscreen-mode .panel-right {
    width: 100%;
    flex: none;
}

.fullscreen-mode .editor-content {
    max-height: calc(100vh - 120px);
}

.fullscreen-mode .request-form {
    max-width: none;
}

/* Editor Header Actions */
.editor-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fullscreen-btn {
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.fullscreen-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.fullscreen-mode .fullscreen-btn {
    background-color: var(--primary-color);
    color: white;
}


.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.list-header h3 {
    margin: 0;
    font-size: 16px;
}

.search-wrapper {
    margin-bottom: 10px;
}

#search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    background-color: var(--input-background);
    color: var(--text-color);
}

.header-actions {
    display: flex;
    gap: 5px;
}

.header-btn {
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: background-color 0.3s, border-color 0.3s;
}

.header-btn.add-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.header-btn.theme-btn {
    background-color: #000000;
    color: #ffffff;
    border: 1px solid #333333;
    transition: all 0.3s ease;
}

.header-btn.theme-btn:hover {
    background-color: #333333;
    transform: scale(1.05);
}

/* Dark theme styling for theme button */
[data-theme="dark"] .header-btn.theme-btn {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
}

[data-theme="dark"] .header-btn.theme-btn:hover {
    background-color: #f0f0f0;
}

.sort-menu {
    position: absolute;
    right: 15px;
    top: 45px;
    background-color: var(--panel-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    min-width: 150px;
}

.sort-menu.hidden {
    display: none;
}

.sort-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
}

.sort-option:hover {
    background-color: var(--input-background);
}

.sort-option.active {
    background-color: var(--primary-color);
    color: white;
}

.requests-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
}

.list-footer {
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: border-color 0.3s;
}

.settings-section {
    margin-top: 10px;
}

.settings-section h5 {
    margin: 0 0 5px 0;
    font-size: 12px;
    color: var(--light-text-color);
    text-align: center;
}

.delete-btn.full-width {
    width: 100%;
}

.delete-btn {
    padding: 8px 12px;
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    width: 100%;
}

.delete-btn:hover {
    opacity: 0.9;
}

.list-footer .delete-btn:focus,
.list-footer .delete-btn:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.4);
    /* Subtle red glow for focus */
}

.import-export-buttons {
    display: flex;
    gap: 5px;
}

.io-btn {
    padding: 8px 12px;
    font-size: 14px;
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    flex-grow: 1;
    text-align: center;
    color: var(--text-color);
    transition: background-color 0.2s ease-in-out, border-color 0.2s;
}

.io-btn:hover {
    background-color: var(--border-color);
}

.request-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 11px;
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--light-text-color);
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border 0.2s ease-in-out;
    border: 1px solid transparent;
}

.request-item:hover {
    background-color: var(--input-background);
    color: var(--text-color);
    border: 1px solid var(--primary-color);
}

.request-item.selected {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    text-shadow: none;
}

.request-item-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 10px;
}

/* --- Styling Fixes for Pin and Remove Buttons --- */

/* 1. Generic Icon Button Styling */
.icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
    font-size: 16px;
    /* Standard icon size */
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: var(--light-text-color);
    transition: background-color 0.2s, color 0.2s;
    flex-shrink: 0;
    /* Prevent shrinking in flex containers */
    font-family: var(--font-family);
    /* Ensure icon font is inherited if needed */
    box-sizing: border-box;
}

.icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.08);
    color: var(--text-color);
}

/* 2. Specific Styling for Pin Icon */
.request-item-pin {
    font-size: 1.2em;
    /* Keep slightly larger size */
}

.request-item-pin.pinned {
    color: var(--primary-color);
}

.request-item:hover .request-item-pin {
    color: var(--text-color);
    /* Already handled by .icon-btn:hover */
}

.request-item.selected .icon-btn,
.request-item.selected .icon-btn:hover {
    color: white;
    background-color: transparent;
}

.request-item.selected .request-item-pin.pinned {
    color: black;
}

/* 3. Specific Styling for Remove Buttons */
.remove-header-btn,
.remove-simple-row-btn {
    background-color: var(--error-color);
    color: white;
    width: 20px;
    height: 20px;
    font-size: 14px;
    /* Adjust size for the 'x' */
    line-height: 20px;
    /* Center the 'x' vertically */
}

.remove-header-btn:hover,
.remove-simple-row-btn:hover {
    background-color: #c0392b;
    /* Darker red on hover */
    color: white;
}

.remove-header-btn.confirm,
.remove-simple-row-btn.confirm {
    background-color: var(--success-color) !important;
    /* Green for confirm, use important to override other styles */
}


.requests-list .empty-state {
    padding: 1rem;
    text-align: center;
    color: var(--light-text-color);
}

.panel-right {
    position: relative;
    flex-grow: 1;
    padding: 10px;
    /* Reduced padding */
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    transition: background-color 0.3s;
    box-sizing: border-box;
}

.request-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#request-editor:focus {
    outline: none;
}


.editor-content {
    overflow-y: auto;
    /* Make the content area scrollable */
    padding: 8px;
    /* Reduced padding */
    min-height: 0;
    /* Prevents grid blowout */
    scrollbar-gutter: stable;
}

.request-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    /* Allow the form to grow and fill the space */
    flex-grow: 1;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
    /* Reduced gap */
}

.request-name-wrapper {
    flex-grow: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.save-name-btn {
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 8px;
    transition: opacity 0.2s;
}

.save-name-btn.hidden {
    opacity: 0;
    pointer-events: none;
}

.request-name-input {
    font-size: 18px;
    font-weight: bold;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 5px;
    background-color: var(--input-background);
    color: var(--text-color);
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    width: 100%;
    /* Ensure it fills the wrapper */
    box-sizing: border-box;
}

.request-name-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.body-type-switcher {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    flex-shrink: 0;
}

#body-type-select {
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--input-background);
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

#body-type-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
    background-color: var(--panel-background);
}

/* Visual indicators for different body types */
.body-section {
    display: flex;
    flex-direction: column;
    padding: 10px;
    border: 2px solid transparent;
    border-radius: 6px;
    transition: border-color 0.2s ease-in-out;
}

.body-section:not(.hidden) {
    border-color: var(--primary-color);
    background-color: rgba(74, 144, 226, 0.05);
}

.advanced-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 12px;
    color: var(--light-text-color);
}

.form-group input,
.form-group select {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-background);
    color: var(--text-color);
    transition: border-color 0.2s ease-in-out, background-color 0.3s, color 0.3s;
}

.url-input-wrapper {
    display: flex;
    align-items: center;
}

.url-input-wrapper input {
    flex-grow: 1;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.test-btn {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--input-background);
    cursor: pointer;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    color: var(--text-color);
    transition: background-color 0.3s, border-color 0.3s;
}

.invalid-field {
    border-color: var(--error-color) !important;
    box-shadow: 0 0 3px var(--error-color);
}

.headers-section {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    background-color: var(--panel-background);
    transition: border-color 0.3s, background-color 0.3s;
}

.headers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.headers-header h4 {
    margin: 0;
    font-size: 14px;
}

.header-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

.add-btn-small {
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

#headers-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.header-item {
    display: flex;
    gap: 5px;
    align-items: center;
}

.header-inputs {
    display: flex;
    flex-grow: 1;
    gap: 5px;
}

.value-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.value-input-wrapper input,
.value-input-wrapper textarea {
    flex-grow: 1;
    padding-right: 30px;
    /* Make space for clear button */
}

/* Special styling for form-data value wrappers */
.form-data-value-wrapper .value-input-wrapper textarea {
    padding-right: 30px;
    /* Ensure space for clear button in form-data fields */
}

.clear-value-btn,
.clear-header-value-btn {
    position: absolute;
    right: 8px;
    top: 12px;
    /* Fixed position from top instead of center for better consistency */
    background-color: var(--light-text-color);
    border: none;
    color: white;
    cursor: pointer;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: none;
    /* Hidden by default */
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    pointer-events: auto;
    z-index: 1000;
    /* Much higher z-index to ensure it's always clickable */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
    /* Stronger shadow for better visibility */
    font-size: 10px;
}

/* Show clear button only when input/textarea has content */
.value-input-wrapper.has-content .clear-value-btn,
.value-input-wrapper.has-content .clear-header-value-btn {
    display: flex;
}

.clear-value-btn:hover,
.clear-header-value-btn:hover {
    background-color: var(--error-color);
    color: white;
    transform: scale(1.15);
    /* Consistent scaling without vertical translation */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.header-item input {
    border: 1px solid var(--border-color);
    padding: 5px;
    border-radius: 3px;
    background-color: var(--input-background);
    color: var(--text-color);
}

.header-item .header-key {
    width: 120px;
    flex-shrink: 0;
}

.header-item .header-value {
    flex-grow: 1;
}

.body-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.body-header h4 {
    margin: 0;
    font-size: 14px;
}

.body-section.hidden {
    display: none;
}

#simple-body-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.simple-body-item {
    display: flex;
    gap: 4px;
    /* Reduced gap */
    align-items: flex-start;
}

.simple-body-item input,
.simple-body-item textarea {
    border: 1px solid var(--border-color);
    padding: 8px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    background-color: var(--input-background);
    color: var(--text-color);
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.3s, color 0.3s;
}

.simple-body-item input:focus,
.simple-body-item textarea:focus,
.form-data-item textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
    background-color: var(--panel-background);
}

.simple-body-item .simple-body-key {
    flex: 1;
    /* Let the key take up 1 part of the available space */
}

.simple-body-item .value-input-wrapper {
    flex: 2;
    /* Let the value take up 2 parts, making it wider */
}

.simple-body-item .simple-body-key {
    resize: none;
    /* Keys should not be manually resizable by default */
    min-height: 38px;
    transition: min-height 0.2s ease-in-out;
}

.simple-body-item .simple-body-key.expanded {
    min-height: 160px;
    resize: vertical;
    /* Expand key field on focus */
}

.simple-body-item .simple-body-value {
    resize: none;
    /* Values should not be manually resizable by default */
    min-height: 38px;
    transition: min-height 0.2s ease-in-out;
}

.simple-body-item .simple-body-value.expanded {
    min-height: 160px;
    resize: vertical;
    /* Expand value field on focus */
}

.char-word-count {
    font-size: 12px;
    color: var(--light-text-color);
    text-align: right;
    margin-top: -10px;
    margin-bottom: 10px;
    padding-right: 5px;
}

.char-word-count.hidden {
    display: none;
}

.body-section h4 {
    margin-top: 0;
    margin-bottom: 5px;
}

textarea {
    width: 100%;
    box-sizing: border-box;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    resize: vertical;
    background-color: var(--input-background);
    color: var(--text-color);
}

.raw-body-toggle {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.footer-links {
    display: flex;
    gap: 15px;
    justify-content: center;
    /* Center the links */
    margin-top: 15px;
    /* Add space above the links */
    width: 100%;
}

.footer-link {
    font-size: 12px;
    color: var(--light-text-color);
    text-decoration: none;
}

.footer-link:hover {
    text-decoration: underline;
}

.footer-link-icon {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.response-viewer {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-top: 15px;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    background-color: var(--input-background);
    border-bottom: 1px solid var(--border-color);
}

.response-header h4 {
    margin: 0;
    font-size: 14px;
}

#response-status {
    font-size: 12px;
    font-weight: bold;
}

#response-status.status-success {
    color: var(--success-color);
}

#response-status.status-error {
    color: var(--error-color);
}

#response-body {
    padding: 10px;
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    /* Increased height for better viewing */
    overflow-y: auto;
    background-color: var(--input-background);
    border-radius: 4px;
    line-height: 1.4;
}

.footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.send-btn {
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
}

.send-btn:hover {
    background-color: #357abd;
    /* A slightly darker blue on hover */
}

.send-btn:active {
    transform: scale(0.98);
}

.send-btn.loading .btn-text {
    display: none;
}

.send-btn.loading::after {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Cancel upload button */
.cancel-upload-btn {
    background: rgba(231, 76, 60, 0.8);
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    margin-left: 8px;
    padding: 4px 8px;
    transition: all 0.2s ease;
}

.cancel-upload-btn:hover {
    background: rgba(231, 76, 60, 1);
    transform: scale(1.05);
}

.cancel-upload-btn:active {
    transform: scale(0.95);
}

/* Progress container styling */
.progress-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
}

/* Enhanced send progress styles */
.send-btn.sending {
    background-color: #f39c12 !important;
    position: relative;
    overflow: hidden;
}

.send-btn.sending .btn-text {
    display: flex;
    align-items: center;
    gap: 6px;
}

.send-btn.sending .progress-indicator {
    width: 12px;
    height: 12px;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

.send-btn.progress {
    background: linear-gradient(90deg, var(--success-color) var(--progress-width, 0%), var(--primary-color) var(--progress-width, 0%));
    transition: none;
}

.send-btn.progress .btn-text::after {
    content: ' (' attr(data-progress) '%)';
    font-size: 11px;
    opacity: 0.9;
}

/* Progress overlay for visual feedback */
.send-btn.upload-progress {
    background: linear-gradient(90deg,
            rgba(46, 204, 113, 0.8) var(--upload-progress, 0%),
            rgba(74, 144, 226, 0.3) var(--upload-progress, 0%));
    transition: --upload-progress 0.3s ease;
}

.send-btn.upload-progress .btn-text {
    position: relative;
    z-index: 2;
}

.send-btn.upload-progress::before {
    content: attr(data-upload-progress) '%';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    opacity: 0.8;
    z-index: 3;
}

.progress-percentage {
    display: inline-block;
    min-width: 30px;
    text-align: right;
    font-size: 12px;
    font-weight: 600;
    color: inherit;
    margin-left: 6px;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.send-btn.sending {
    background-color: #f39c12 !important;
    position: relative;
}

.send-btn.sending::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.keep-data-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 12px;
    color: var(--light-text-color);
}

#keep-data-checkbox {
    margin: 0;
}

.form-data-item {
    display: grid;
    grid-template-columns: 200px 1fr auto;
    /* controls | value | remove */
    gap: 8px;
    align-items: start;
    /* Changed from center to start for better alignment with file upload areas */
    margin-bottom: 12px;
    /* Increased spacing for better visual separation */
}

.form-data-item select,
.form-data-item input {
    border: 1px solid var(--border-color);
    padding: 8px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    background-color: var(--input-background);
    color: var(--text-color);
}

.form-data-controls {
    display: grid;
    grid-template-columns: 70px minmax(80px, 1fr);
    /* type | key (shorter key to free space) */
    gap: 6px;
    align-items: center;
    min-width: 0;
}

.form-data-value-wrapper {
    position: relative;
    min-width: 0;
    display: flex;
    align-items: stretch;
    /* Changed to stretch for file upload areas */
    gap: 6px;
    overflow: visible;
    /* ensure floated elements are fully visible */
    flex: 1;
    max-width: 100%;
}

.form-data-item .form-data-type {
    flex-shrink: 0;
}

.form-data-item .form-data-key,
.form-data-item .form-data-value,
.form-data-item textarea.form-data-value,
.form-data-item textarea.form-data-key {
    width: 100%;
    box-sizing: border-box;
    border: 1px solid var(--border-color);
    padding: 8px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    background-color: var(--input-background);
    color: var(--text-color);
}

.form-data-item textarea.form-data-key {
    resize: none;
    min-height: 38px;
    transition: min-height 0.2s ease-in-out;
}

.form-data-item textarea.form-data-key.expanded {
    min-height: 120px;
    resize: vertical;
    /* Expand on focus */
}

.form-data-item textarea.form-data-value {
    resize: none;
    min-height: 38px;
    transition: min-height 0.2s ease-in-out;
    font-family: inherit;
    font-size: 14px;
}

.form-data-item textarea.form-data-value.expanded {
    min-height: 120px;
    resize: vertical;
    /* Expand on focus */
}

/* Enhanced file upload styling */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background-color: var(--panel-background);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(74, 144, 226, 0.05);
}

.file-upload-area.drag-over {
    border-color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.upload-icon {
    font-size: 24px;
    opacity: 0.6;
}

.upload-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.upload-main {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.upload-hint {
    font-size: 12px;
    color: var(--light-text-color);
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 12px;
    color: var(--text-color);
    font-weight: 500;
}

/* Enhanced file display */
.form-data-file-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--panel-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    gap: 12px;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    transition: all 0.2s ease;
}

.form-data-file-display:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-grow: 1;
    min-width: 0;
}

/* File thumbnail styling */
.file-thumbnail {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--input-background);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.file-thumbnail:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.file-icon {
    font-size: 24px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background-color: var(--input-background);
}

.file-icon.video-file {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
}

.file-icon.image-file {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
}

.file-icon.placeholder-file {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.file-icon.fresh-upload {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    animation: successPulse 2s ease-in-out;
}

@keyframes successPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
    flex-grow: 1;
    overflow: hidden;
    max-width: 100%;
}

.file-size {
    font-size: 11px;
    color: var(--light-text-color);
    font-weight: 500;
}

.file-type-label {
    font-size: 11px;
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
    width: fit-content;
}

.file-type-label.warning {
    color: #f39c12;
    font-weight: 700;
}

.file-type-label.success {
    color: var(--success-color);
    font-weight: 700;
    animation: successGlow 1.5s ease-in-out;
}

@keyframes successGlow {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* Animated success tick mark */
.animated-success {
    font-size: 18px;
    font-weight: bold;
    color: white;
    animation: tickPop 0.6s ease-out;
    display: inline-block;
}

@keyframes tickPop {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    50% {
        transform: scale(1.3);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.form-data-filename {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
    max-width: 100%;
    min-width: 0;
    width: 100%;
}

.filename-start {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
    min-width: 0;
    max-width: calc(100% - 50px);
    /* Reserve space for extension */
}

.filename-ext {
    font-weight: 600;
    color: var(--primary-color);
    flex-shrink: 0;
    margin-left: auto;
}

.clear-file-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.clear-file-btn:hover {
    background-color: #c0392b;
    transform: scale(1.1);
}

/* Update existing form-data styles to work with new structure */
/* Legacy file input styling - keeping minimal for compatibility */

/* Enhanced file display replaces old filename styling */

.remove-form-data-row-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    font-size: 14px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.remove-form-data-row-btn:hover {
    background-color: #c0392b;
}

.remove-form-data-row-btn.confirm {
    background-color: var(--success-color) !important;
}

/* New: dedicated clear-file “x” button styling */
/* Small top-right 'x' above the filename to free horizontal space */
/* Float the clear button above the filename so it doesn't collide with the row delete */
.clear-file-btn {
    position: absolute;
    top: -8px;
    /* raise it a bit more */
    right: -6px;
    /* nudge right so it doesn't sit under the red '-' */
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 0 1px #fff;
    z-index: 5;
    /* ensure it stacks above neighbors */
}

.clear-file-btn:hover {
    background-color: #c0392b;
}

.clear-file-btn:hover {
    background-color: #c0392b;
}

/* Ensure textareas/inputs don’t push layout when filenames are long */
.form-data-item .form-data-key,
.form-data-item .form-data-value,
.form-data-item textarea.form-data-value,
.form-data-item textarea.form-data-key {
    min-width: 0;
    /* allow shrinking */
}