---
trigger: always_on
alwaysApply: true
---
Dont create any html file to test out Ask the user to test out and come up with the  results. And if the results are not expected then the next step is to do proper logging to the code so that you can figure out exactly whats going wrong.Also I like to create apps in modular waySo that its easy to Maintain And debug.So if possibleTry to create modular structureFor the app. I am using windows and Powershell so  use the commands for that. Always getApproval from the user before you make the changeLayout your plan ask the userWhat they want then only implement What they approve.And remove the debugging code once the issue is fixed.When you add new logging remove all the other old logging and test file  which is not necessary Only keep logging which is required to fix the current issue